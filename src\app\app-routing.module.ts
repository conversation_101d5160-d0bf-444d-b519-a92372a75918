import {NgModule} from '@angular/core'
import {PreloadAllModules, RouterModule, Routes} from '@angular/router'

const routes: Routes = [
  { path: 'index', loadChildren: () => import('./pages/index/index.module').then((m) => m.IndexPageModule) },
  { path: 'login', loadChildren: () => import('./pages/login/login.module').then((m) => m.LoginPageModule) },
  // {
  //   path: 'lb-admin',
  //   loadChildren: () => import('./pages/LbAdmin/tabs/tabs.module').then((m) => m.TabsPageModule),
  // },
  // {
  //   path: 'lb-reader',
  //   loadChildren: () => import('./pages/LbReader/tabs/tabs.module').then((m) => m.TabsPageModule),
  // },

  {
    path: 'item-detail',
    loadChildren: () => import('./pages/item-detail/item-detail.module').then((m) => m.ItemDetailPageModule),
    // loadChildren: () => import('./pages/tabs/tabs.module').then((m) => m.TabsPageModule),
  },

  // { path: 'tab', loadChildren: () => import('./pages/tab-item/tab-item.module').then((m) => m.TabItemPageModule) },

  { path: 'setting', loadChildren: () => import('./pages/setting/setting.module').then((m) => m.SettingPageModule) },
  { path: 'function', loadChildren: () => import('./pages/function/function.module').then((m) => m.FunctionPageModule) },


  {
    path: '',
    redirectTo: '/index',
    pathMatch: 'full',
  },
  { path: 'inventory-list', loadChildren: () => import('./pages/inventory/inventory-list/inventory-list.module').then((m) => m.InventoryListPageModule) },
  { path: 'inventory-item-list/:id',
    loadChildren: () => import('./pages/inventory/inventory-item-list/inventory-item-list.module').then((m) => m.InventoryItemListPageModule) },
  // { path: 'inventory-scanner', loadChildren: () => import('./pages/inventory/inventory-scanner/inventory-scanner.module').then((m) => m.InventoryScannerPageModule) },


  { path: 'cancellation-list', loadChildren: () => import('./pages/cancellation/cancellation-list/cancellation-list.module').then((m) => m.CancellationListPageModule) },
  { path: 'cancellation-item-list/:id',
    loadChildren: () => import('./pages/cancellation/cancellation-item-list/cancellation-item-list.module').then((m) => m.CancellationItemListPageModule) },

  { path: 'relocation-list', loadChildren: () => import('./pages/relocation/relocation-list/relocation-list.module').then((m) => m.RelocationListPageModule) },
  { path: 'relocation-item-list/:id',
    loadChildren: () => import('./pages/relocation/relocation-item-list/relocation-item-list.module').then((m) => m.RelocationItemListPageModule) },

  { path: 'allocation-search', loadChildren: () => import('./pages/allocation/allocation-search/allocation-search.module').then((m) => m.AllocationSearchPageModule) },
  { path: 'allocation-item-list',
    loadChildren: () => import('./pages/allocation/allocation-item-list/allocation-item-list.module').then((m) => m.AllocationItemListPageModule) },


  { path: 'borrow-search', loadChildren: () => import('./pages/borrow/borrow-search/borrow-search.module').then((m) => m.BorrowSearchPageModule) },
  { path: 'borrow-item-list', loadChildren: () => import('./pages/borrow/borrow-item-list/borrow-item-list.module').then((m) => m.BorrowItemListPageModule) },

  { path: 'return-search', loadChildren: () => import('./pages/return/return-search/return-search.module').then((m) => m.ReturnSearchPageModule) },
  { path: 'return-item-list', loadChildren: () => import('./pages/return/return-item-list/return-item-list.module').then((m) => m.ReturnItemListPageModule) },

  // { path: 'inventory-search', loadChildren: () => import('./pages/inventory/inventory-search/inventory-search.module').then((m) => m.InventorySearchPageModule) },

  // { path: 'select-location', loadChildren: () => import('./pages/inventory/select-location/select-location.module').then((m) => m.SelectLocationPageModule) },

]

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {preloadingStrategy: PreloadAllModules})
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
