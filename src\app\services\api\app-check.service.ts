import { Injectable } from '@angular/core';
import {RequestService} from '../request/request.service'

@Injectable({
  providedIn: 'root'
})
export class AppCheckService {


  constructor(
    public request: RequestService,
  ) {
  }
  private demoCodes = [
    '1czmp6xj',
    'mqzwnkzz',
    'ex18e3k8',
    'dm9y8er6',
    'amru8pzv',
    'zduy7n1b',
  ]
  /**
   * (Setting頁面)訪問Norray中心驗證Company Identifier
   */
  getEncCompanyUUID(
    cmpyUUID: string, //
    device: string, //
  ) {
    return new Promise((resolve, reject) => {
      const host = this.demoCodes.includes(cmpyUUID) ? 'https://www.runcops.com' : 'http://www.esaccount.com'
      this.request.request({
        url: `${host}/AssetUserCenter/index.php/AppCheck/getEncCompanyUUID?cmpyUUID=${cmpyUUID}&device=${device}`,
        method: 'POST',
        responseType: 'full',
        data: {}
      }).then((res: any) => {
        if (res && res.code === 1000) {
          resolve(res)
        } else {
          reject(res)
        }
        // resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 獲取Asset的公司Code
   */
  getcmpyCode(
    host: string, //
    project: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `${host}/${project}/index.php/AppCheck/getCmpyCode`,
        method: 'POST',
        responseType: 'source',
        data: {}
      }).then(res => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 訪問Norray中心綁定設備uuid
   */
  confirmCompanyUUID(
    cmpyUUID: string, //
    device: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `http://www.esaccount.com/AssetUserCenter/index.php/AppCheck/confirmCompanyUUID?cmpyUUID=${cmpyUUID}&device=${device}`,
        method: 'POST',
        responseType: 'full',
        data: {}
      }).then((res: any) => {
        console.log(res)
        if (res.code === 1000) {
          resolve(res)
        } else {
          reject(res.msg)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 檢查是否連接Asset Server成功
   */
  checksetting(
    host: string, //
    project: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `${host}/${project}/index.php/AppLogin/checksetting`,
        method: 'POST',
        responseType: 'full',
        data: {}
      }).then((res: any) => {
        console.log(res)
        if (res.status === 'success') {
          resolve(res)
        } else {
          reject(res.msg)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

}
