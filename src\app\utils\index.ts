import * as dateUtils from './dateUtils'

export function sleep(time) {
  return new Promise(resolve => setTimeout(resolve, time))
}

export function itemRemove(selector: string) {
  return new Promise(resolve => {
    // const el = (window.document.querySelector(selector) as HTMLElement)
    // el.classList.remove('item-enter')
    // el.classList.add('item-leave')
    // el.classList.remove('item-enter')
    // el.classList.add('item-leave')
    const els = window.document.querySelectorAll(selector)
    els.forEach((el: HTMLElement) => {
      el.classList.remove('item-enter')
      el.classList.add('item-leave')
    })
    if (els.length) {
      sleep(300).then(() => {
        // @ts-ignore
        resolve()
      })
    } else {
      // @ts-ignore
      resolve()
    }
  })
}

export function dateFormat(date, format = 'yyyy-MM-dd') {
  return dateUtils.format(date, format)
}
