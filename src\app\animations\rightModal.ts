import { createAnimation, ModalAnimationOptions } from '@ionic/core';
import { Animation, AnimationController } from '@ionic/angular';

export const enterAnimation = (baseEl: HTMLElement) => {
    const root = baseEl.shadowRoot;

    const backdropAnimation = new AnimationController()
      .create()
      .addElement(root.querySelector('ion-backdrop')!)
      .fromTo('opacity', '0.01', 'var(--backdrop-opacity)');

    const wrapperAnimation = new AnimationController()
      .create()
      .addElement(root.querySelector('.modal-wrapper')!)
      .keyframes([
        { offset: 0, opacity: '0', transform: 'translateX(100%)' },
        { offset: 1, opacity: '0.99', transform: 'translateX(0%)' },
      ]);

    return new AnimationController()
      .create()
      .addElement(baseEl)
      .easing('cubic-bezier(0.36,0.66,0.04,1)')
      .duration(250)
      .addAnimation(backdropAnimation)
      .addAnimation(wrapperAnimation);
  };

export const leaveAnimation = (baseEl: HTMLElement) => {
return enterAnimation(baseEl).direction('reverse');
}
export function rightEnterAnimation(baseEl: HTMLElement, opts: ModalAnimationOptions): Animation {

    const baseAnimation = createAnimation();

    const backdropAnimation = createAnimation();
    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop'));

    const wrapperAnimation = createAnimation();
    wrapperAnimation.addElement(baseEl.querySelector('.modal-wrapper'));
    wrapperAnimation.beforeStyles({ opacity: 1 })
        // .fromTo('opacity', 0, 1)
        .fromTo('translateX', '100%', '0%');

    backdropAnimation.fromTo('opacity', 0.01, 0.4);

    return baseAnimation
        .addElement(baseEl)
        .easing('cubic-bezier(0.36,0.66,0.04,1)')
        .duration(250)
        .beforeAddClass('show-modal')
        .addAnimation(backdropAnimation)
        .addAnimation(wrapperAnimation);

}

export function rightLeaveAnimation(baseEl: HTMLElement, opts: ModalAnimationOptions): Animation {

    const baseAnimation = createAnimation();

    const backdropAnimation = createAnimation();
    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop'));

    const wrapperAnimation = createAnimation();
    const wrapperEl = baseEl.querySelector('.modal-wrapper');
    wrapperAnimation.addElement(wrapperEl);
    // const wrapperElRect = wrapperEl.getBoundingClientRect();

    wrapperAnimation.beforeStyles({opacity: 1})
        // .fromTo('translateX', '0%', `${window.innerWidth - wrapperElRect.left}px`);
        .fromTo('translateX', '0%', `100%`);

    backdropAnimation.fromTo('opacity', 0.4, 0.0);

    return baseAnimation
        .addElement(baseEl)
        .easing('ease-out')
        .duration(400)
        .addAnimation(backdropAnimation)
        .addAnimation(wrapperAnimation);

}

const animationCtrl = new AnimationController();

export const getIonPageElement = (element: HTMLElement) => {
  if (element.classList.contains('ion-page')) {
    return element;
  }

  const ionPage = element.querySelector(
    ':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs'
  );
  if (ionPage) {
    return ionPage;
  }
  // idk, return the original element so at least something animates and we don't have a null pointer
  return element;
};
export const fancyAnimation = (_: HTMLElement, opts: any) => {
  const backDirection = opts.direction === 'back';
  const enteringEl = opts.enteringEl;
  const leavingEl = opts.leavingEl;

  const enteringPageEl = getIonPageElement(enteringEl);

  const rootTransition = animationCtrl.create();

  const enterTransition = animationCtrl.create();
  const leavingTransition = animationCtrl.create();

  leavingTransition.addElement(getIonPageElement(leavingEl)).duration(250);

  enterTransition
    .addElement(enteringPageEl)
    .duration(250)
    // .fill('both')
    // .beforeRemoveClass('ion-page-invisible');

  if (!backDirection) {
    enterTransition
      // .beforeStyles({ border: 'thin solid black' })
      .keyframes([
        { offset: 0, transform: 'translateX(100%)' },
        { offset: 1, transform: 'translateX(0%)' }
      ])
      // .afterClearStyles(['border']);

    leavingTransition.keyframes([
      { offset: 0, opacity: 1 },
      { offset: 1, opacity: 0.1 }
    ]);
  } else {
    enterTransition.keyframes([
      { offset: 0, opacity: 0.1 },
      { offset: 1, opacity: 1 }
    ]);

    leavingTransition
      // .beforeStyles({ border: 'thin solid black' })
      .keyframes([
        { offset: 0, transform: 'translateX(0%)' },
        { offset: 1, transform: 'translateX(100%)' }
      ])
      // .afterClearStyles(['border']);
  }

  rootTransition.addAnimation([enterTransition, leavingTransition]);

  return rootTransition;
};
