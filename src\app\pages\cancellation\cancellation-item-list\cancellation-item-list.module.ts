import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { CancellationItemListPage } from './cancellation-item-list.page';

const routes: Routes = [
  {
    path: '',
    component: CancellationItemListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [CancellationItemListPage],
  // entryComponents: []
})
export class CancellationItemListPageModule {}
