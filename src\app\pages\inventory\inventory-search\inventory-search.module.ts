import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { InventorySearchPage } from './inventory-search.page';
import { CardModule } from '@app/components/card/card.module'
// const routes: Routes = [
//   {
//     path: '',
//     component: InventorySearchPage
//   }
// ];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes),
    CardModule,
  ],
  // declarations: [InventorySearchPage]
})
export class InventorySearchPageModule {}
