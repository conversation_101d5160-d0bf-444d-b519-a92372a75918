ion-content {
  --background: var(--content-page-background);
}
.content {
  padding: 0.5rem;
  background: var(--content-page-background);
  height: 100%;
  width: 100%;
  padding-top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top));
  padding-right: calc(env(safe-area-inset-right) + 0.5rem);
  padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
  padding-bottom: env(safe-area-inset-bottom); /* 在 iphone x + 中本句才会生效 */
  padding-left: calc(env(safe-area-inset-left) + 0.5rem);

  overflow: auto;

  .sys-info {
    text-align: center;
    position: relative;
    width: 100%;
    height: 20%;
    .sys-logo {
      height: 70%;
      width: auto;
    }
    .sys-name {
      height: 1.5rem;
    }
  }


  .back-btn {
    text-align: right;
    position: absolute;
    right: 0.5rem;
    top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top) + 0.5rem);
    ion-button {
      font-size: 0.7rem;
      height: 1.5rem;

    }
  }

  .body {
    .actions {
    }
  }

}
