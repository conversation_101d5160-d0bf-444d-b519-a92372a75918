.asset-card {
  margin: 1rem 0 ;
  border-radius: 0.5rem;
  background: #ffffff;
  overflow: hidden;
  .asset-card-header {
    background: #519BD4;
    color: #ffffff;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    &.disabled {
      background: #C8C8C9;
    }
  }
  .asset-card-body {
    padding: 0.5rem 1rem;

    ::ng-deep {
      .label {
        font-size: 0.7rem;
      }
      ion-input {
        border: 1px solid #E2E3E3;
        border-radius: 5px;
        font-size: 0.7rem;
        line-height: 1.5rem;
        height: 1.7rem;
      }

      .actions {
        text-align: center;

        ion-button {

          width: 5rem;
          margin: 0.5rem 1rem;
          font-size: 0.7rem;
          line-height: 1rem;
          padding: 0rem;
          height: 1.5rem;
        }
      }
    }
  }
}
