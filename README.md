# Asset-Ionic
Asset-Ionic


## 通用搭建
```
npm install -g cordova ionic
ionic start myApp tabs
cd myApp
// ionic serve

// 0.安裝依賴
npm install

// 1.添加平台
ionic cordova platform add android
// ionic cordova platform add ios
// 2.編譯
ionic cordova prepare android
// ionic cordova prepare ios
// 3.測試運行
ionic cordova run android -l --external
// ionic cordova run ios -l --address=0.0.0.0

// 4.android 打包
ionic cordova build android --prod --release
keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore HelloWorld-release-unsigned.apk alias_name
zipalign -v 4 HelloWorld-release-unsigned.apk HelloWorld.apk



```



### 錯誤處理

#### 1
```
[cordova] BUILD FAILED in 1s
[cordova] F:\program\Asset-Ionic\platforms\android\gradlew: Command failed with exit code 1 Error output:
[cordova] FAILURE: Build failed with an exception.
[cordova]
[cordova] * What went wrong:
[cordova] A problem occurred configuring project ':app'.
[cordova] > Failed to install the following Android SDK packages as some licences have not been accepted.
[cordova]      build-tools;28.0.3 Android SDK Build-Tools 28.0.3
[cordova]   To build this project, accept the SDK license agreements and install the missing components using the Android Studio SDK Manager.
```
```
C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin
sdkmanager --licenses


%ANDROID_HOME%/tools/bin/sdkmanager --licenses
```
