import {Injectable} from '@angular/core'

import {AlertController, NavController, Platform} from '@ionic/angular'

import {Router} from '@angular/router'
import {AuthService} from '../auth/auth.service'
import {StorageService} from '../storage/storage.service'
import {HTTP} from '@ionic-native/http/ngx'
import {LoadingService} from '../../utils/loading.service'
import {StorageNativeService} from '@services/storageNative/storage-native.service'

@Injectable({
  providedIn: 'root'
})
export class RequestService {


  constructor(
    private platform: Platform,
    private router: Router,
    private nav: NavController,
    private alertController: AlertController,
    private authService: AuthService,
    private storageService: StorageService,
    private loading: LoadingService,
    private nativeStorage: StorageNativeService,
    private httpService: HTTP) {
  }

  async request(obj: any) {
    const that = this
    const headers: any = {}
    // this.httpService.setDataSerializer('json');
    if (this.authService.getToken() != null && this.authService.getToken() !== '') {
      // console.log(getToken())
      headers.authorization = this.authService.getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    headers.language = 'zh-hk'// this.storageService.store.language ||
    headers.device = this.storageService.store.device || 'Android'
    try {
      const user_id = await this.nativeStorage.getItem('user_id')
      // headers.operator = ''
      headers.operator = user_id || ''
    } catch (e) {
      console.error(e)
    }
    // headers.system = this.storageService.serverSetting.webCode


    let url = obj.url
    if (!/:\/\//.test(url)) {
      try {
        const realHost = await this.nativeStorage.getItem('realHost')
        const project = await this.nativeStorage.getItem('project')
        url = realHost + '/' + project + '/index.php' + obj.url
        // url = 'http://**************/ASSET-WISDOM/index.php' + obj.url
        // console.log(url)
      } catch (e) {
        console.error(e)
        return {msg: 'No server setting set!'}
      }
    }

    if (obj.method.toLowerCase() === 'get') {
      const params: any = {}
      for (const key in obj.params) {
        if (obj.params.hasOwnProperty(key) && obj.params[key] !== undefined) {
          params[key] = obj.params[key]
        }
      }
      return new Promise((resolve, reject) => {
        this.httpService.get(url, params, headers)
          .then(response => {
            // debugger;
            // return response.data;
            // resolve(JSON.parse(response.data))
            try {
              // const data = JSON.parse(response.data)
              // switch (data.code) {
              //   case 200:
              //   case 1000:
              //     if (obj.responseType === 'full') {
              //       resolve(data)
              //     } else {
              //       resolve(data.data)
              //     }
              //     break
              //   case 401:
              //     // reject('未登錄')
              //     that.reLoginAlert()
              //     break
              //   case 403:
              //   // reject('沒有權限')
              //   // break
              //   case 404:
              //   // reject('請求地址錯誤')
              //   // break
              //   default:
              //     reject(data.msg)
              //     break
              // }
              const result = that.formatResponse(response, obj)
              if (result.success) {
                resolve(result.data)
              } else {
                reject(result.data)
              }
            } catch (e) {
              reject('服務器響應數據錯誤')
            }
          })
          .catch(err => {
            switch (typeof err) {
              case 'string':
              case 'number':
                reject(err.toString())
                break
              case 'object':
                if (err.message) {
                  reject(err.message)
                } else {
                  reject(JSON.stringify(err))
                }
                break
              default:
                reject(JSON.stringify(err))
                break
            }
          })
      })

    } else {
      // const url = 'http://192.168.88.10/seschool/public/api/se/login'
      // const url = 'http://test.esaccount.com/EDSchoolCenter-LB/web/lb/readers/login'
      let postData = {}
      if (typeof obj.data === 'object') {
        if (obj.data instanceof FormData) {
          postData = obj.data
        } else {
          postData = {
            _format: 'json',
          ...obj.data
          }
        }
      } else {
        postData = obj.data
      }
      return new Promise((resolve, reject) => {
        this.httpService.post(url, postData, headers)
          .then(response => {
            try {
              // const data = JSON.parse(response.data)
              // switch (data.code) {
              //   case 200:
              //   case 1000:
              //     if (obj.responseType === 'full') {
              //       resolve(data)
              //     } else {
              //       resolve(data.data)
              //     }
              //     break
              //   case 401:
              //     // reject('未登錄')
              //     that.reLoginAlert()
              //     break
              //   case 403:
              //   // reject('沒有權限')
              //   // break
              //   case 404:
              //   // reject('請求地址錯誤')
              //   // break
              //   default:
              //     reject(data.msg)
              //     break
              // }
              const result = that.formatResponse(response, obj)
              if (result.success) {
                resolve(result.data)
              } else {
                reject(result.data)
              }
            } catch (e) {
              reject('服務器響應數據錯誤')
            }
          })
          .catch(err => {
            switch (typeof err) {
              case 'string':
              case 'number':
                reject(err.toString())
                break
              case 'object':
                if (err.message) {
                  reject(err.message)
                } else {
                  reject(JSON.stringify(err))
                }
                break
              default:
                reject(JSON.stringify(err))
                break
            }
          })
      })
      //   .then(response => {
      //     resolve(response)
      //   }).catch(async error => {
      //     const alertObj = await this.alertController.create({
      //       header: '提示',
      //       message: error.message,
      //       buttons: [{
      //         text: '好'
      //       }]
      //     });
      //     await alertObj.present();
      //     reject()
      //   })
      // })
    }
  }
  formatResponse(res, params) {
    if (params.responseType === 'source') {
      return { success: true, data: res.data }
    }
    let data: any = {}
    try {
      data = JSON.parse(res.data)
    } catch (e) {
      return { success: true, data: res.data.data }
    }
    if (params.responseType === 'full') {
      // return data
      return { success: true, data }
    }
    switch (data.code) {
      case 200:
      case 1000:
        return { success: true, data: data.data }
      case 401:
        // reject('未登錄')
        this.reLoginAlert()
        return { success: false, data: res.data }
      case 403:
      // reject('沒有權限')
      // break
      case 404:
      // reject('請求地址錯誤')
      // break
      default:
        // return data.msg
        return { success: false, data: data.msg ? data.msg : data }
    }
  }

  async reLoginAlert() {
    this.loading.end()
    const alertObj = await this.alertController.create({
      header: 'Alert',
      message: 'This account is already logged in elsewhere',
      buttons: [{
        text: 'OK',
        handler: () => {
          // this.router.navigate(['login'])
          location.href = '/login'
          // this.nav.setTopOutlet('/login')
        }
      }]
    })
    await alertObj.present()
    await alertObj.onDidDismiss()
  }

  responseHandler(response) {

  }
}
