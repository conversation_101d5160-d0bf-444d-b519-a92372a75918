import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ItemDetailPage } from './item-detail.page';

const routes: Routes = [
  {
    path: '',
    component: ItemDetailPage,
    children: [
      {
        path: 'item',
        children: [
          {
            path: '',
            loadChildren: () => import('./item-detail-item/item-detail-item.module').then((m) => m.ItemDetailItemPageModule)
          }
        ]
      },
      {
        path: 'access',
        children: [
          {
            path: '',
            loadChildren: () => import('./item-detail-access/item-detail-access.module').then((m) => m.ItemDetailAccessPageModule)
          }
        ]
      },
      {
        path: 'maint',
        children: [
          {
            path: '',
            loadChildren: () => import('./item-detail-maint/item-detail-maint.module').then((m) => m.ItemDetailMaintPageModule)
          }
        ]
      },
      {
        path: 'other',
        children: [
          {
            path: '',
            loadChildren: () => import('./item-detail-other/item-detail-other.module').then((m) => m.ItemDetailOtherPageModule)
          }
        ]
      },
      {
        path: '',
        redirectTo: '/item-detail/item',
        // redirectTo: '/lb-admin/tab1',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ItemDetailPageRoutingModule {}
