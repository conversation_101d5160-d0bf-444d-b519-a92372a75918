<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onBackToHome()">Back</ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onSave()">Submit</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="full">
    <!--<ion-item class="item-header" lines="full">-->
      <!--<div class="item-box">-->
      <!--  <span class="info">item info</span>-->
      <!--  <span class="qty">Qty</span>-->
      <!--  <span class="stq">Stock Take Qty</span>-->
      <!--  <span class="aw">Apply Wo</span>-->
      <!--</div>-->
      <!--<ion-row>-->
      <!--  <ion-col size="6" class="info ion-text-center">Item info</ion-col>-->
      <!--  <ion-col size="1" class="qty ion-text-center">Qty</ion-col>-->
      <!--  <ion-col size="1.7" class="stq ion-text-center">Stock Take Qty</ion-col>-->
      <!--  <ion-col size="1.3" class="aw ion-text-center">Apply Wo</ion-col>-->
      <!--  <ion-col size="2" class="action ion-text-center"></ion-col>-->
      <!--</ion-row>-->
    <!--</ion-item>-->
    <!--<ion-item class="item-data" lines="full" *ngFor="let item of data.list; let i = index;">-->
    <!--  <ion-row>-->
    <!--    <ion-col size="6" class="info">-->
    <!--      <span>{{ item.name }}</span>-->
    <!--      <span>Location Name: {{ item.locationname }}</span>-->
    <!--      <span>Bar Code No: {{ item.property_no }}</span>-->
    <!--      <span>Searial No: {{ item.property_location_serial_serial }}</span>-->
    <!--      <span class="location" (click)="onChangeLocation(item)">{{ item.asset_match_locationname }}<ion-icon icon="create"></ion-icon></span>-->
    <!--    </ion-col>-->
    <!--    <ion-col size="1" class="qty">{{ item.property_qty }}</ion-col>-->
    <!--    <ion-col size="1.7" class="stq" (click)="onChangeSTQ(item)">{{ item.stocktakeqty }}</ion-col>-->
    <!--    <ion-col size="1.3" class="aw" (click)="onChangeAW(item)">-->
    <!--      <span *ngIf="isAW(item) else showNum">{{ item.asset_check_match_w_num }}</span>-->
    <!--      <ng-template #showNum>-->
    <!--        <ion-icon icon="add" ></ion-icon>-->
    <!--      </ng-template>-->
    <!--    </ion-col>-->
    <!--    <ion-col size="2" class="action"><ion-button color="asset" (click)="onConfirm(item)">Confirm</ion-button></ion-col>-->
    <!--  </ion-row>-->
    <!--</ion-item>-->
    <ion-list-header lines="full">
      <ion-label>Article Information</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">Barcode No.</ion-label>
      <ion-text class="item-value">{{ data.property_barcode }}</ion-text>
    </ion-item>
    <ion-item lines="full" detail (click)="searchItem()">
      <ion-label position="fixed">Article Name</ion-label>
      <ion-text class="item-value">{{ data.property_name }}</ion-text>
      <ion-text slot="end" class="item-value placeholder">Detail</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Origin Department</ion-label>
      <ion-text class="item-value">{{ data.old_department_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Origin Category</ion-label>
      <ion-text class="item-value">{{ data.old_kind_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Location</ion-label>
      <ion-text class="item-value">{{ data.location_name || getLocationName(data.property_location) }}</ion-text>
    </ion-item>
    <ion-item-divider></ion-item-divider>
    <ion-list-header lines="full">
      <ion-label>Department/Category Allocation</ion-label>
    </ion-list-header>
    <ion-item lines="full" detail (click)="onSelectDepartment()">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>New Department</ion-label>
      <ion-text *ngIf="data.department_no" class="item-value">{{ data.department_name }}</ion-text>
      <ion-text *ngIf="!data.department_no" class="item-value placeholder">Please Choose</ion-text>
    </ion-item>
    <ion-item lines="full" detail (click)="onSelectKind()">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>New Category</ion-label>
      <ion-text *ngIf="data.kind_no" class="item-value">{{ data.kind_name }}</ion-text>
      <ion-text *ngIf="!data.kind_no" class="item-value placeholder">Please Choose</ion-text>
    </ion-item>
    <!--<ion-item [button]="false" [detail]="false" lines="full">-->
    <!--  <ion-label position="fixed"><ion-text color="danger">*</ion-text>報銷選項</ion-label>-->
    <!--  <div style="width: 100%">-->
    <!--    <ion-select [(ngModel)]="data.property_status_change_woreason" class="item-value" placeholder="Please Choose" okText="確認" cancelText="取消" style="width: 100%; max-width: 100%;">-->
    <!--      <ion-select-option value="0">損壞</ion-select-option>-->
    <!--      <ion-select-option value="1">遺失</ion-select-option>-->
    <!--      <ion-select-option value="2">被盜</ion-select-option>-->
    <!--      <ion-select-option value="3">轉增</ion-select-option>-->
    <!--      <ion-select-option value="4">轉讓</ion-select-option>-->
    <!--      <ion-select-option value="5">更換</ion-select-option>-->
    <!--      <ion-select-option value="6">失修</ion-select-option>-->
    <!--      <ion-select-option value="7">過期</ion-select-option>-->
    <!--      <ion-select-option value="8">退貨</ion-select-option>-->
    <!--      <ion-select-option value="10">消耗</ion-select-option>-->
    <!--      <ion-select-option value="9">其他</ion-select-option>-->
    <!--    </ion-select>-->
    <!--  </div>-->
    <!--</ion-item>-->
    <ion-item lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>Relocation qty.</ion-label>
      <ion-input [(ngModel)]="data.change_num" type="number" placeholder="Please Fill Qty."></ion-input>
    <!-- class="ion-text-right"    (ionFocus)="onFocus($event)" -->
    </ion-item>
    <!--<ion-item lines="full">-->
    <!--  <ion-label position="fixed"><ion-text color="danger">*</ion-text>報銷時圖片</ion-label>-->
    <!--  <div class="img-box" tappable>-->
    <!--    <div class="img-wrapper">-->
    <!--      <img *ngIf="data.img" [src]="data.img" alt="" (click)="imgViewer()">-->
    <!--      <div *ngIf="!data.img" class="add-img" (click)="addImg()">-->
    <!--        <ion-icon icon="add" ></ion-icon>-->
    <!--        &lt;!&ndash;<ion-icon [icon]="data.img ? 'remove-circle-outline' : 'add'" ></ion-icon>&ndash;&gt;-->
    <!--      </div>-->
    <!--      <div *ngIf="data.img" class="remove-img" (click)="removeImg()"><ion-icon icon="close-circle-outline"></ion-icon></div>-->
    <!--    </div>-->
    <!--  </div>-->
    <!--</ion-item>-->
    <ion-item lines="full">
      <ion-label position="fixed">Remark</ion-label>
      <ion-textarea [(ngModel)]="data.property_status_change_remark" class="remark-input" placeholder="Remark"></ion-textarea>
    </ion-item>
  </ion-list>
</ion-content>
