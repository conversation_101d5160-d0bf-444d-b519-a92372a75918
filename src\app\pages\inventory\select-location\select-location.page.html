<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onCancel()">
        Cancel
      </ion-button>
    </ion-buttons>
    <ion-title>Select New Location</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list *ngIf="list && list.length > 0">
    <ion-item *ngFor="let item of list" lines="full" [detail]="item.isParent === 'true'" button (click)="onClickItem(item)">
      <ion-label>{{ item.name }}</ion-label>
      <ion-icon *ngIf="item.isParent === 'false'" icon="radio-button-off" slot="end"></ion-icon>
    </ion-item>
  </ion-list>

</ion-content>
