.info-box {
  padding: 0.7rem 0;
  width: 70%;
  z-index: 11;
  span.info-item {
    display: block;
    text-overflow: ellipsis;
    font-size: 0.5rem;
    &.no {
      font-size: 0.5rem;
    }
  }

}
.img-box {
  width: 80px;
  height: 80px;
  .img-wrapper {
    .add-img {
      height: 80px;
      vertical-align: middle;
      line-height: 66px;
      display: inline-block;
    }
    ion-icon, img {

      width: 70px;
      height: 70px;
      color: #e8e8e8;
      border: 1px solid #e8e8e8;
      line-height: 30px;
      vertical-align: top;
      display: inline-block;
      margin: 5px;
    }

  }
}
ion-list {
  ion-item-sliding {
    ion-item {
      padding: 0;
    }
  }
  &.list-ios {
    margin-bottom: 0;
  }
}



.header-btn {
  background: #519BD4;
  color: #FFFFFF;
  --ion-toolbar-color: #FFF;
  border-radius: 5px;
}

.header-row {
  //padding-right: 20px;
  --background: #CBCBCB;
  background: #CBCBCB;
  .label {
    text-align: right;
    width: 100%;
  }
  ::ng-deep {
    ion-checkbox {
      margin-right: 20px;
    }
  }
}
.ion-page {
  // padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  //padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
  //padding-bottom: calc(env(safe-area-inset-bottom) + 0px); /* 在 iphone x + 中本句才会生效 */
  padding-left: env(safe-area-inset-left);
  // --background: #ffffff;
  // --content-page-background: #ffffff;
}
::ng-deep {
  ion-router-outlet {
    background: #ffffff;
  }
}
ion-content {
  // --background: #ffffff;
  // --content-page-background: #ffffff;
}
