<div [class]="className">
  <ion-toolbar
      class="scanner-bar-tool"
      [ngClass]="currentClass"
  >
    <ion-label slot="start" class="label">{{ label }}</ion-label>
    <form class="form">
      <ion-input
          [(ngModel)]="value"
          name="value"
          class="form-input"
          inputmode="search"
          type="search"
          [clearInput]="true"
          [autocorrect]="true"
          [placeholder]="placeholder"
          (change)="handleChange()"
          (ionBlur)="handleSubmit()"
          (keyup.enter)="handleSubmit()"
      ></ion-input>
    </form>
    <ion-buttons *ngIf="showRightScanner" slot="end">
      <ion-button class="icon-btn" (click)="handleScan()">
<!--        <ion-icon class="icon" name="qr-scanner"></ion-icon>-->

        <i class="iconfont icon-scan right"></i>
      </ion-button>
    </ion-buttons>
<!--        <ion-icon class="icon" slot="end" name="qr-scanner"></ion-icon>-->
  </ion-toolbar>
  <div *ngIf="showBottomScanner" class="scanner-btn">
    <i class="iconfont icon-scan bottom" (click)="handleScan()"></i>
  </div>
</div>
