import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AllocationSearchPage } from './allocation-search.page';

describe('AllocationSearchPage', () => {
  let component: AllocationSearchPage;
  let fixture: ComponentFixture<AllocationSearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AllocationSearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AllocationSearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
