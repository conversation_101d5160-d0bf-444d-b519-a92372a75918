import { Injectable } from '@angular/core';
import {RequestService} from '../request/request.service'

@Injectable({
  providedIn: 'root'
})
export class SchoolService {

  constructor(public request: RequestService) {
  }


  /**
   * 學校簡介列表
   */
  getschool() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppLogin/getschool`,
        method: 'GET',
        responseType: 'full'
      }).then((res: any) => {
        console.log(res)
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }

}
