import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { RelocationItemListPage } from './relocation-item-list.page';

const routes: Routes = [
  {
    path: '',
    component: RelocationItemListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [RelocationItemListPage],
  // entryComponents: []
})
export class RelocationItemListPageModule {}
