.item-header {
  ion-row {
    width: 100%;
    ion-col {
      justify-content: center;
      justify-items: center;
      display: flex;
      align-items: center;
      font-size: 15px;
    }
  }
}

.item-data {
  font-size: 13px;
  ion-row {
    width: 100%;
    ion-col {
      span {
        display: block;
        ion-icon {
          font-size: inherit;
          line-height: inherit;
          vertical-align: middle;
        }
        &.location {
          color: #5891c0;
        }
      }
      &:not(.info) {
        justify-content: center;
        justify-items: center;
        display: flex;
        align-items: center;
      }
      &.stq {
        color: #bcca7a;
      }
      &.aw {
        color: #b5313a;
      }

      ion-button {
        font-size: 9px;
        max-width: 100%;
        font-size: 0.4rem;
        min-height: 25px;
      }
    }
  }
}


.header-btn {
  background: #519BD4;
  color: #FFFFFF;
  --ion-toolbar-color: #FFF;
  border-radius: 5px;
}
