<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onNoteList()">Back</ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onSave()">Submit</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="full">
    <!--<ion-item class="item-header" lines="full">-->
      <!--<div class="item-box">-->
      <!--  <span class="info">item info</span>-->
      <!--  <span class="qty">Qty</span>-->
      <!--  <span class="stq">Stock Take Qty</span>-->
      <!--  <span class="aw">Apply Wo</span>-->
      <!--</div>-->
      <!--<ion-row>-->
      <!--  <ion-col size="6" class="info ion-text-center">Item info</ion-col>-->
      <!--  <ion-col size="1" class="qty ion-text-center">Qty</ion-col>-->
      <!--  <ion-col size="1.7" class="stq ion-text-center">Stock Take Qty</ion-col>-->
      <!--  <ion-col size="1.3" class="aw ion-text-center">Apply Wo</ion-col>-->
      <!--  <ion-col size="2" class="action ion-text-center"></ion-col>-->
      <!--</ion-row>-->
    <!--</ion-item>-->
    <!--<ion-item class="item-data" lines="full" *ngFor="let item of data.list; let i = index;">-->
    <!--  <ion-row>-->
    <!--    <ion-col size="6" class="info">-->
    <!--      <span>{{ item.name }}</span>-->
    <!--      <span>Location Name: {{ item.locationname }}</span>-->
    <!--      <span>Bar Code No: {{ item.property_no }}</span>-->
    <!--      <span>Searial No: {{ item.property_location_serial_serial }}</span>-->
    <!--      <span class="location" (click)="onChangeLocation(item)">{{ item.asset_match_locationname }}<ion-icon icon="create"></ion-icon></span>-->
    <!--    </ion-col>-->
    <!--    <ion-col size="1" class="qty">{{ item.property_qty }}</ion-col>-->
    <!--    <ion-col size="1.7" class="stq" (click)="onChangeSTQ(item)">{{ item.stocktakeqty }}</ion-col>-->
    <!--    <ion-col size="1.3" class="aw" (click)="onChangeAW(item)">-->
    <!--      <span *ngIf="isAW(item) else showNum">{{ item.asset_check_match_w_num }}</span>-->
    <!--      <ng-template #showNum>-->
    <!--        <ion-icon icon="add" ></ion-icon>-->
    <!--      </ng-template>-->
    <!--    </ion-col>-->
    <!--    <ion-col size="2" class="action"><ion-button color="asset" (click)="onConfirm(item)">Confirm</ion-button></ion-col>-->
    <!--  </ion-row>-->
    <!--</ion-item>-->
    <ion-list-header lines="full">
      <ion-label>Article Information</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">Barcode No.</ion-label>
      <ion-text class="item-value">{{ data.property_barcode }}</ion-text>
    </ion-item>
    <ion-item lines="full" detail (click)="searchItem()">
      <ion-label position="fixed">Article Name</ion-label>
      <ion-text class="item-value">{{ data.property_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Location</ion-label>
      <ion-text class="item-value">{{ data.location_name || getLocationName(data.property_location) }}</ion-text>
    </ion-item>
    <ion-item-divider></ion-item-divider>
    <ion-list-header lines="full">
      <ion-label>Relocation</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>Qty.</ion-label>
      <ion-input [(ngModel)]="data.change_num" type="number" placeholder="Please Fill Qty."></ion-input>
    <!-- class="ion-text-right"    (ionFocus)="onFocus($event)" -->
    </ion-item>
    <ion-item lines="full" detail (click)="onSelectLocation()">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>New Location</ion-label>
      <ion-text *ngIf="data.relocation_location_no" class="item-value">{{ getLocationName(data.relocation_location_no) }}</ion-text>
      <ion-text *ngIf="!data.relocation_location_no" class="item-value placeholder">Please Choose</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Remark</ion-label>
      <!--<ion-text>{{ data.property_relocation_remark }}</ion-text>-->
      <ion-textarea [(ngModel)]="data.property_relocation_remark" class="remark-input" placeholder="Remark"></ion-textarea>
    </ion-item>
  </ion-list>
</ion-content>
