import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { BorrowItemListPage } from './borrow-item-list.page';

const routes: Routes = [
  {
    path: '',
    component: BorrowItemListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [BorrowItemListPage],
  // entryComponents: []
})
export class BorrowItemListPageModule {}
