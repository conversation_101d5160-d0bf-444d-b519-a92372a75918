<ion-content>
  <div class="content">
    <div class="sch-info">
      <ion-img *ngIf="settingInit" [src]="LogoImg" [class]="logoClassName" (ionError)="onLogoError($event)" (ionImgDidLoad)="onLogoLoad()"></ion-img>
      <div *ngIf="settingInit && schcname" class="sch-name-cn">{{ schcname }}</div>
      <div *ngIf="settingInit && schename" class="sch-name-en">{{ schename }}</div>

    </div>
    <div class="asset-info">
      <ion-img src="assets/images/logo.png" class="sys-logo"></ion-img>
      <div class="sys-name">{{ 'HOME.SYS_NAME' | translate }}</div>
      <div class="version">{{ 'HOME.VERSION' | translate }} {{ version }}</div>
      <div class="btn-group">
        <div class="left">
          <ion-button
              color="asset"
              style="width: 7rem;font-size: 0.7rem;height: 1.5rem;"
              (click)="toSetting()">{{ 'BUTTON.SETTING' | translate }}</ion-button>
        </div>
        <div class="right">
          <ion-button
              color="asset"
              [disabled]="!canlogin"
              style="width: 7rem;font-size: 0.7rem;height: 1.5rem;"
              (click)="toLogin()">{{ 'BUTTON.ENTRY' | translate }}</ion-button>
        </div>
      </div>
      <div class="app-footer">
        Powered By : {{ poweredBy}}
        <br> Hotline : {{ hotline }}
      </div>
    </div>

  </div>
</ion-content>
