import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InventoryScannerPage } from './inventory-scanner.page';

describe('InventoryScannerPage', () => {
  let component: InventoryScannerPage;
  let fixture: ComponentFixture<InventoryScannerPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InventoryScannerPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InventoryScannerPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
