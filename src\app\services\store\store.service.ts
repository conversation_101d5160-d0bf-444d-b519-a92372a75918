import {Injectable} from '@angular/core'
import {AlertController} from '@ionic/angular'
import Cookies from 'js-cookie'
import {StorageService} from '../storage/storage.service'
import {AuthService} from '../auth/auth.service'
import {LoginService} from '../api/login.service'
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import md5 from 'md5'

@Injectable({
  providedIn: 'root'
})
export class StoreService {
  constructor(
    private alertController: AlertController,
    private storageService: StorageService,
    private loginService: LoginService,
    private authService: AuthService,
    private nativeStorage: StorageNativeService
  ) {
    //
  }

  async sleep(time) {
    await setTimeout(() => {
    }, time)
    return true
  }

  // async GetStore() {
    // return new Promise((resolve, reject) => {
    // const data = this.storageService.get()
    // // const data = this.storageService.store;
    // if (data.school === null || data.school.school_id === '') {
      // await this.sleep(2000)
      // await this.GetSchool()
      // return this.storageService.get()
    // } else {
    //   resolve(data)
      // return data
    // }
    // })
  // }
  permissionFormat(res) {
    let persimmions: any = {}
    if (res.LB) {
      persimmions.LB = '1'
      const ps = this.permissionFormatByObj(res.LB.permission)
      persimmions = Object.assign(persimmions, ps)
    }
    if (res['LB-READER']) {
      persimmions['LB-READER'] = '1'
      const ps = this.permissionFormatByObj(res['LB-READER'].permission)
      persimmions = Object.assign(persimmions, ps)
    }
    return persimmions
  }
  permissionFormatByObj(res) {
    const persimmions = {}
    for (const item of res) {
      const p = Object.assign(item)
      const key = p.permission_code


      const content = p.permission_content.split('')
      const selection = p.permission_selection ? p.permission_selection.split('') : content.map(() => '0')
      const markCn = p.permission_mark_cn.split(',')
      const markEn = p.permission_mark_en.split(',')
      const items = content.map((c, index) => {
          return {
            name_cn: markCn[index],
            name_en: markEn[index],
            selected: selection[index],
            content: content[index]
          }
        })

      p.content = content
      p.selection = selection
      p.markCn = markCn
      p.markEn = markEn
      p.items = items
      persimmions[key] = p

}
    return persimmions
  }


  // Login(userInfo: any) {

    // const password = md5(userInfo.password)
    // const username = userInfo.username.trim()
    // const login_role = userInfo.login_type === 'MANAGER' ? 'MANAGER' : userInfo.login_type
    // const login_role_code = '1' // (login_role === 'MANAGER' ? '0' : '1')
    //
    // let user_id = ''
    // let school_year_id = ''
    // return new Promise((resolve, reject) => {
    //   // 登錄
    //   this.loginService.login(username, password, login_role_code)
    //     .then((data: any) => {
    //       // if (response && response.code === 200) {
    //       //   const data: any = response.data
    //         this.authService.setToken(data.token)
    //         this.storageService.set('userId', data.user_id)
    //         this.storageService.set('login_role', login_role)
    //
    //         this.nativeStorage.setItem('userInfo', Object.assign({}, data))
    //         this.nativeStorage.setItem('username', username)
    //         this.nativeStorage.setItem('password', userInfo.password)
    //
    //         user_id = data.user_id
    //         return Promise.resolve(data.user_id)
    //       // } else {
    //       //   // reject(response.message)
    //       //   return Promise.reject(response.message)
    //       // }
    //     })
    //     // 獲取用戶權限
    //     .then(() => {
    //       this.nativeStorage.setItem('permissions', {})
    //       return this.userService.getPermissionsForApp({ user_id, have_selection: '1', independent_system: '1'})
    //     })
    //     .then((data: any) => {
    //       const permissions = this.permissionFormat(data)
    //       this.nativeStorage.setItem('permissions', Object.assign({}, permissions))
    //       // resolve(data)
    //     })
    //     // 獲取當前學年
    //     .then(() => this.yearService.getCurrentYear())
    //     .then((data: any) => {
    //       this.nativeStorage.setItem('currentYear', Object.assign({}, data))
    //       school_year_id = data.school_year_id
    //       // resolve(data)
    //     })
    //     // 獲取用戶信息
    //     .then(() => this.userService.getInfo(user_id))
    //     .then((data: any) => {
    //         this.nativeStorage.setItem('userInfo', Object.assign({}, data))
    //         resolve(data)
    //     })
    //     .then(() => {
    //       return this.readersService.findReaderByUser({
    //         school_year_id,
    //         user_id
    //       })
    //     })
    //     .then((data: any) => {
    //       this.nativeStorage.setItem('readerInfo', Object.assign({}, data))
    //     })
    //     .catch(error => {
    //       reject(error)
    //     })
    // })
  // }
  async initPermissions() {
    // try {
    //   const user = await this.nativeStorage.getItem('userInfo')
    //   if (!user || user.user_id) { return false }
    //   const user_id = user.user_id
    //   const data = await this.userService.getPermissionsForApp({user_id, have_selection: '1', independent_system: '1'})
    //   const permissions = this.permissionFormat(data)
    //   this.nativeStorage.setItem('permissions', Object.assign({}, permissions))
    // } catch (e) {
    //   console.error(e)
    // }
  }

  // Logout() {
    // return new Promise((resolve) => {
    //   this.storageService.set('userId', '')
    //   this.authService.removeToken()
    //   this.nativeStorage.setItem('userInfo', {
    //     userId: '',
    //     ipAddress: '',
    //     clientIp: '',
    //     loginRole: ''
    //   })
    //   resolve()
    // })
  // }

  // GetSchool() {
  //   return new Promise((resolve, reject) => {
  //     this.loginService.getSchool()
  //       .then(async (data: any) => {
  //         // const alertObj = await this.alertController.create({
  //         //   header: '提示',
  //         //   message: JSON.stringify(response),
  //         //   buttons: [{
  //         //     text: '好'
  //         //   }]
  //         // });
  //         // await alertObj.present();
  //         const language = Cookies.get('language') || Object.keys(data.language[0])[0]
  //         this.storageService.set('school', data)
  //         this.storageService.set('language', language)
  //         this.nativeStorage.setItem('school', data)
  //         // const alertObj = await this.alertController.create({
  //         //   header: '提示',
  //         //   message: '1212121',
  //         //   buttons: [{
  //         //     text: '好'
  //         //   }]
  //         // });
  //         // await alertObj.present();
  //         resolve(data)
  //       })
  //       .catch(async error => {
  //         const alertObj = await this.alertController.create({
  //           header: '提示',
  //           message: JSON.stringify(error),
  //           buttons: [{
  //             text: '好'
  //           }]
  //         })
  //         await alertObj.present()
  //         reject(error)
  //       })
  //   })
  // }
}
