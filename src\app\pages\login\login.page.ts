import {Component, OnInit, ViewChild} from '@angular/core'
import {Router} from '@angular/router'
import {AlertController, IonInput, LoadingController, NavController, Platform, ToastController} from '@ionic/angular'

import {StorageService} from '../../services/storage/storage.service'
import {StoreService} from '../../services/store/store.service'
import {UtilsService} from '../../utils/utils.service'
import {TranslateService} from '@ngx-translate/core'
import {AssetService} from '@services/api/asset.service'
import {LoadingService} from '@app/utils/loading.service'
import {RelocationService} from '@services/sql/relocation.service'
import {PermissionService} from '@services/permissions/permission.service'
import {StorageNativeService} from '@services/storageNative/storage-native.service'

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
})
export class LoginPage implements OnInit {

  public shouldHeight = document.body.clientHeight - 56 + 'px'
  public subscription: any
  private backButtonPressed = false

  public serverInfo: any
  public store: any
  public loginForm: any = {
    username: '',
    password: '',
  }
  public poweredBy = ''
  public hotline = ''

  @ViewChild('pwd', {}) pwdInput: IonInput

  constructor(
    private platform: Platform,
    private router: Router,
    public loadingController: LoadingController,
    private alertController: AlertController,
    private storageService: StorageService,
    private storeService: StoreService,
    private nativeStorage: StorageNativeService,
    public toastController: ToastController,
    public nav: NavController,
    public utils: UtilsService,
    public translate: TranslateService,
    public asset: AssetService,
    public loadingService: LoadingService,
    public relocationService: RelocationService,
    public permissionService: PermissionService,
  ) {
    this.poweredBy = this.storageService.appInfo.poweredBy
    this.hotline = this.storageService.appInfo.hotline
    this.platform.ready()
      .then(() => {
      })
  }

  async ngOnInit() {

    // this.store = await this.storeService.GetStore()
  }

  get logoUrl() {
    if (this.serverInfo && this.store && this.store.school) {
      return this.serverInfo.realHost + '/' + this.serverInfo.remoteProjectName + '/'
          + this.serverInfo.uri + '/' + this.store.school.school_logo
    } else {
      return ''
    }
  }

  get schNameCn() {
    if (this.store && this.store.school) {
      return this.store.school.school_name_cn
    } else {
      return ''
    }
  }

  get now() {
    return new Date().getTime() + this.store.school.school_name_cn
  }

  get schNameEn() {
    if (this.store && this.store.school) {
      return this.store.school.school_name_en
    } else {
      return ''
    }
  }

  get language() {
    // let language = this.storageService.get('language')
    // if (language) {
    //   return language
    // } else {
    //   return 'zh-hk'
    // }
    return 'zh-hk'
  }

  toSetting() {
    this.router.navigate(['setting'])
  }

  toHome() {
    this.router.navigate(['home'])
  }

  toFunction() {
    this.nav.navigateRoot('/function')
    this.router.navigate(['function'])
    // this.nav.navigateRoot('/function')
  }

  get canSubmit() {
    return this.loginForm?.username?.length > 0 && this.loginForm?.password?.length > 0
  }

  async handleSuccess() {
    const toast = await this.toastController.create({
      message: this.translate.instant('LOGIN.LOGIN_SUCCESS'),
      duration: 2000,
      position: 'middle',
    })
    toast.present()
  }

  async handleLogin() {
    if (!this.canSubmit) {
      return
    }

    if (this.loginForm.username !== '' && this.loginForm.password !== '') {
      const username = this.loginForm.username
      const password = this.loginForm.password
      // const loading = await this.loadingController.create({
      //   message: this.translate.instant('LOGIN.LOADING'),
      //   translucent: true,
      //   cssClass: 'custom-class custom-loading',
      // })
      // await loading.present()
      await this.loadingService.start()

      // loading.dismiss();
      // this.toHome()
      // return
      // this.storeService.Login({username, password}).then(() => {
      //   this.router.navigate(['setting'])
      //   loading.dismiss()
      //   // this.handleSuccess()
      //   this.saveLastUserName(username)
      //   // this.router.navigate(['select_system']);
      //   this.toHome()
      // })
      //   .catch(async error => {
      //     console.log(error)
      //     loading.dismiss()
      //     let msg = 'Error'
      //     switch (typeof error) {
      //       case 'object':
      //         if (error.error) {
      //           msg = error.error.toString()
      //         } else {
      //           msg = JSON.stringify(error)
      //         }
      //         break
      //       case 'bigint':
      //       case 'boolean':
      //       case 'number':
      //       case 'string':
      //         msg = error.toString()
      //         break
      //       default:
      //         msg = this.translate.instant('LOGIN.LOGIN_FAILED')
      //         break
      //     }
      //     const alertObj = await this.alertController.create({
      //       header: this.translate.instant('MESSAGE.TIPS'),
      //       message: msg,
      //       buttons: [{
      //         text: this.translate.instant('BUTTON.CONFIRM'),
      //       }],
      //     })
      //     await alertObj.present()
      //   })
      //   .finally(() => {
      //     loading.dismiss()
      //   })


      this.asset.login(username, password)
        .then(async (res: any) => {
          console.log(res)
          if (res.status === 'success' && res.user_id) {
            await this.nativeStorage.setItem('user_id', res.user_id)
            await this.nativeStorage.setItem('lastLoginName', username)
            // 保存全部地點
            try {
              await this.relocationService.remoteLoadingLocation()
            } catch (e) {
              console.error(e)
            }
            try {
              const permissions = await this.asset.getPolicy()
              await this.nativeStorage.setItem('permissions', permissions)
              await this.permissionService.init()
            } catch (e) {
              console.error(e)
            }
            await this.loadingService.end()
            this.toFunction()
            return Promise.resolve()
          } else {
            // this.utils.showMsg('登錄失敗，請檢查用戶名或密碼！')
            return Promise.reject('Loading Failed!')
          }
        }).catch(err => {
          console.log(err)
          this.utils.showMsg('Login failed, please check username or password')
          this.loadingService.end()
      }).finally(() => {
      })
    }
  }

  saveLastUserName(name) {
    this.nativeStorage.setItem('lastLoginName', name)
  }

  async getLastUserName() {
    try {
      return await this.nativeStorage.getItem('lastLoginName')
    } catch (e) {
      return ''
    }
  }


  handleNextInput() {
    this.pwdInput.setFocus()
  }


  initializeBackButtonCustomHandler(): void {
    // @ts-ignore
    // this.subscription = this.platform.backButton.subscribeWithPriority(9999, () => {
    // })

    // 兩次後退 退出APP
    this.subscription = this.platform.backButton.subscribeWithPriority(9999, () => {
      if (this.backButtonPressed) {
        // @ts-ignore
        navigator.app.exitApp()
      } else {
        this.handleExitSuccess()
        // 标记为true
        this.backButtonPressed = true
        // 两秒后标记为false，如果退出的话，就不会执行了
        setTimeout(() => this.backButtonPressed = false, 2000)
      }
    })

  }

  ionViewDidEnter() {
    this.shouldHeight = document.body.clientHeight - 56 + 'px'
  }
  ionViewWillEnter() {
    this.platform.ready()
      .then(async () => {
        this.initData()
        // this.initializeBackButtonCustomHandler()
      })
  }

  ionViewWillLeave() {
    if (this.subscription) {
      this.subscription.unsubscribe()
      this.subscription = null
    }
  }

  async handleExitSuccess() {
    const toast = await this.toastController.create({
      message: this.translate.instant('MESSAGE.BACK_EXIT_TIPS'),
      duration: 2000,
      // position: 'middle'
      position: 'bottom',
    })
    toast.present()
  }

  initData() {

    // this.loginForm.password = 'admin'
    this.getLastUserName()
      .then(name => {
        this.loginForm.username = name
      })
      // .then(() => this.nativeStorage.getItem('username'))
      // .then(username => this.loginForm.username = username)
      // .then(() => this.nativeStorage.getItem('password'))
      // .then(password => this.loginForm.password = password)
      // .then(() => {
      //   if (this.loginForm.username && this.loginForm.password) {
      //     // this.checkCanSubmit()
      //     return this.handleLogin()
      //   }
      // })
      .catch(err => {
      })
  }
  // handForgetPassword() {
  //   this.utils.showToast(this.translate.instant('LOGIN.CONTACT_ADMIN'))
  // }

  onSubmit() {
    // this.toFunction()
    this.handleLogin()
  }

  onBack() {
    // this.nav.pop()
    // this.nav.navigateRoot('/index')

    this.nav.navigateBack('index')
  }
}
