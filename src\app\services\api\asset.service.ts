import { Injectable } from '@angular/core';
import {RequestService} from '@services/request/request.service'

@Injectable({
  providedIn: 'root'
})
export class AssetService {

  constructor(
    public request: RequestService,
  ) {
  }



  /**
   * 登錄系統
   */
  login(
    usern: string, //
    pwd: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppLogin/login`,
        method: 'POST',
        responseType: 'full',
        data: {
          usern,
          pwd,
          isReturnUserId: 1
        }
      }).then((res: any) => {
        console.log(res)
        if (res.status === 'success') {
          resolve(res)
        } else {
          reject(res.msg)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }



  /**
   * 學校簡介列表
   */
  getSysProfile() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCheck/getSysProfile`,
        method: 'GET',
        responseType: 'full'
      }).then((res: any) => {
        console.log(res)
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }


  /**
   * 4.1(Search物品)獲取Asset物品
   */
  getItemUrl(
    codeno: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/Appitem/backusurl`,
        method: 'POST',
        responseType: 'full',
        data: {
          codeno,
        }
      }).then((res: any) => {
        console.log(res)
        if (Array.isArray(res) && res.length > 0) {
          const item = res[0]
          if (!(item.propertyno === 'None' && item.propertyname === 'None')) {
            resolve(item)
          }
        }
        reject(res)
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 5.1(Search盤點單)獲取Asset盤點單信息
   */
  checkListNo(
    checkno: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCheck/forcheckno`,
        method: 'POST',
        responseType: 'full',
        data: {
          checkno,
        }
      }).then((res: any) => {
        if (res && Array.isArray(res) && res.length > 0) {
          resolve(res[0])
        } else {
          reject()
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 5.2(Search盤點單)獲取Asset盤點單中的物品列表
   */
  getItemList(
    checkno: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCheck/getitemlist`,
        method: 'POST',
        responseType: 'full',
        data: {
          checkno,
        }
      }).then((res: any) => {
        if (res && typeof res === 'object' && res[checkno]) {
          resolve(res[checkno])
        } else {
          reject(res)
        }
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 5.3(進行盤點)獲取Asset中的地點
   */
  getLocation(
    id: string, //
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCheck/appGetNodes`,
        method: 'POST',
        responseType: 'full',
        data: {
          id,
        }
      }).then((res: any) => {
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 5.4(進行盤點)提交盤點單
   */
  saveStockTakeList(
    stocktakedata,
    user_id,
    is_submit
  ) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCheck/savestocktakelist`,
        method: 'POST',
        responseType: 'full',
        data: {
          stocktakedata,
          user_id,
          is_submit,
        }
      }).then((res: any) => {
        if (res && res.status === 'success') {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

  /**
   * 獲取所有地點
   */
  getAppRelocationLocation() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppRelocation/getLocation`,
        method: 'GET',
        responseType: 'full',
      }).then((res: any) => {
        if (res && Array.isArray(res)) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

  /**
   * 物品轉移- 獲取物品信息
   */
  // tslint:disable-next-line:no-unnecessary-initializer
  getAppRelocationInfo(property_barcode, property_location = undefined) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppRelocation/info`,
        method: 'GET',
        params: {
          property_barcode,
          property_location
        },
        responseType: 'full',
      }).then((res: any) => {
        if (res && res.property_no) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

  /**
   * 提交物品轉移
   */
  createRelocation(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppRelocation/create`,
        method: 'POST',
        responseType: 'full',
        data: post_data,
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 檢查物品轉移
   */
  checkRelocation(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppRelocation/check`,
        method: 'POST',
        responseType: 'full',
        data: post_data,
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /*
   *  註銷檢查
   */
  checkCancellation(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCancellation/check`,
        method: 'POST',
        responseType: 'full',
        data: post_data
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /*
   *  註銷創建
   */
  createCancellation(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppCancellation/create`,
        method: 'POST',
        responseType: 'full',
        data: post_data
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }


  /**
   * 獲取所有部門
   */
  getAppDepartments() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppPropertyAllocation/getDepartment`,
        method: 'GET',
        responseType: 'full',
      }).then((res: any) => {
        if (res && Array.isArray(res)) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 獲取所有部門
   */
  getAppKinds() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppPropertyAllocation/getKind`,
        method: 'GET',
        responseType: 'full',
      }).then((res: any) => {
        if (res && Array.isArray(res)) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

  /*
   *  註銷創建
   */
  createPropertyAllocation(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppPropertyAllocation/create`,
        method: 'POST',
        responseType: 'full',
        data: post_data
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

  /**
   * 獲取所有部門
   */
  getAppStaffs() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppBorrow/getStaff`,
        method: 'GET',
        responseType: 'full',
      }).then((res: any) => {
        if (res && Array.isArray(res)) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }

  /*
   *  註銷創建
   */
  createBorrow(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppBorrow/create`,
        method: 'POST',
        responseType: 'full',
        data: post_data
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /*
   *  註銷創建
   */
  createReturn(post_data) {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppPropertyReturn/create`,
        method: 'POST',
        responseType: 'full',
        data: post_data
      }).then((res: any) => {
        if (res && res.status === 200) {
          resolve(res)
        } else {
          reject(res)
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
  /**
   * 獲取權限
   */
  getPolicy() {
    return new Promise((resolve, reject) => {
      this.request.request({
        url: `/AppLogin/getPolicy`,
        method: 'GET',
        responseType: 'full',
      }).then((res: any) => {
        if (res) {
          resolve(res)
        } else {
          resolve({
            AssetCheck: '1111',
            Cancellation: '0000',
            Relocation: '0000',
            PropertyAllocation: '0000',
            Borrow: '0000',
            PropertyReturn: '0000',
          })
          // reject(res)
        }
      }).catch(err => {
        resolve({
          AssetCheck: '1111',
          Cancellation: '0000',
          Relocation: '0000',
          PropertyAllocation: '0000',
          Borrow: '0000',
          PropertyReturn: '0000',
        })
        // reject(err)
      })
    })
  }
}
