<ion-content>
  <div class="content">
    <div class="sys-info">
      <ion-img src="assets/images/logo.png" class="sys-logo"></ion-img>
      <div class="sys-name">{{ 'SYSTEM.NAME' | translate }}</div>
    </div>
    <div class="back-btn">
      <ion-button
          color="asset"
          style=""
          (click)="onBack()">{{ 'BUTTON.BACK' | translate }}</ion-button>
    </div>

    <!-- Company -->
    <app-card>
      <span class="header">{{ 'SETTING.COMPANY' | translate }}</span>
      <div class="body">
        <span class="label" >{{ 'SETTING.COMPANY_IDENTIFIER' | translate }}</span>
        <ion-input
            [(ngModel)]="cmpyUUID"
            [type]="cmpyInit ? 'password' : 'text'"
            [disabled]="cmpyInit"
            clear-input></ion-input>
        <div class="actions">
          <ion-button *ngIf="cmpyInit else elseCmpy" color="asset" (click)="onResetCmpyUUID()">{{ 'BUTTON.RESET' | translate }}</ion-button>
          <ng-template #elseCmpy>
            <ion-button
              [disabled]="companyLoading"
              color="asset"
              (click)="onConfirmCmpyUUID()">{{ 'BUTTON.CONFIRM' | translate }}</ion-button>
          </ng-template>
        </div>
      </div>
    </app-card>
    <!-- Server Setting -->
    <app-card [disabled]="!cmpyInit">
      <span class="header">{{ 'SETTING.SERVER_SETTING' | translate }}</span>
      <div class="body">
        <span class="label" >{{ 'SETTING.SERVER_IP' | translate }}</span>
        <ion-input
            [(ngModel)]="host"
            [disabled]="!cmpyInit"
            clear-input></ion-input>
        <span class="label" >{{ 'SETTING.SERVER_PROJECT_NAME' | translate }}</span>
        <ion-input
            [(ngModel)]="project"
            [disabled]="!cmpyInit"
            clear-input></ion-input>
        <span class="label" >{{ 'SETTING.STOCK_TAKE_CONFIRMATION' | translate }}</span>
        <ion-toggle [(ngModel)]="stockTakeConfirmation" [disabled]="!cmpyInit" color="success" style="vertical-align: middle;"></ion-toggle>
        <div class="actions">
          <ion-button
            color="asset"
            [disabled]="!cmpyInit || serverLoading"
            (click)="onCheckSetting()">{{ 'BUTTON.CONFIRM' | translate }}</ion-button>
        </div>
      </div>
    </app-card>
    <div class="app-footer">
      Powered By : {{ poweredBy }}
      <br> Hotline : {{ hotline }}
    </div>
  </div>
</ion-content>
