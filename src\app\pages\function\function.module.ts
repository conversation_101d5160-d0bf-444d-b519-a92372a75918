import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { FunctionPage } from './function.page';
import { CardModule } from '@app/components/card/card.module'
import { EmptyModule } from '@app/components/empty/empty.module'
const routes: Routes = [
  {
    path: '',
    component: FunctionPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    CardModule,
    EmptyModule,
  ],
  declarations: [FunctionPage]
})
export class FunctionPageModule {}
