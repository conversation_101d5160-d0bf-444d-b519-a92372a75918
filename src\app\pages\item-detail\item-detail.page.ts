import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router'
import {Platform} from '@ionic/angular'
import {LoadingService} from '@app/utils/loading.service'
import {UtilsService} from '@app/utils/utils.service'
import {TranslateService} from '@ngx-translate/core'
import {AssetService} from '@services/api/asset.service'


@Component({
  selector: 'app-item-detail',
  templateUrl: './item-detail.page.html',
  styleUrls: ['./item-detail.page.scss'],
})
export class ItemDetailPage implements OnInit {

  public codeno = ''
  public tab = ''
  public data: any = {}
  constructor(
    private routeInfo: ActivatedRoute,
    private platform: Platform,
    private loading: LoadingService,
    public utils: UtilsService,
    public asset: AssetService,
    public translate: TranslateService,
    private router: Router,
    ) { }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.tab = 'item'
    this.codeno = ''
    this.data = {}
  }

  ionViewDidEnter() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      this.codeno = this.routeInfo.snapshot.queryParams.codeno
      this.loadData()
    })
  }


  async loadData() {
    try {
      await this.loading.start()
      this.data = await this.asset.getItemUrl(this.codeno)
    } catch (e) {
      this.utils.showToast(e)
    }
    await this.loading.end()

  }
  routeToTab(tab) {
    let url = ''
    switch (tab) {
      case 'item':
        this.tab = 'item'
        url = this.data.itemurl
        break
      case 'access':
        this.tab = 'access'
        url = this.data.accessurl
        break
      case 'maint':
        this.tab = 'maint'
        url = this.data.mainturl
        break
      case 'other':
        this.tab = 'other'
        url = this.data.otherurl
        break
      default:
        return
    }
    this.router.navigate(
      ['/item-detail/' + tab],
      {
        queryParams: {
          url,
        },

      }
    )
  }
  selectedTab(tabName) {
    if (this.tab === tabName) {
      return 'selected-tab'
    }
    return ''
  }
}
