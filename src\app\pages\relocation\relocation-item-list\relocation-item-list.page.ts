import {ApplicationRef, Component, OnInit} from '@angular/core'
import {Platform, AlertController, NavController, ModalController} from '@ionic/angular'
import {ActivatedRoute, Router} from '@angular/router'
import {UtilsService} from '@app/utils/utils.service'
import {TreeSelectPage} from '@app/components/tree-select/tree-select.page'
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import {RelocationService} from '@services/sql/relocation.service'
import {EventService} from '@services/event/event.service'
import {LoadingService} from '@app/utils/loading.service'
import {AssetService} from '@services/api/asset.service'

@Component({
  selector: 'app-relocation-item-list',
  templateUrl: './relocation-item-list.page.html',
  styleUrls: ['./relocation-item-list.page.scss'],
})
export class RelocationItemListPage implements OnInit {

  public id = ''
  public data: any = {}
  public confirmation = true
  public locations: any = []
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    private relocationService: RelocationService,
    public asset: AssetService,
    private router: Router,
    private loading: LoadingService,
    private eventService: EventService,
    private alertController: AlertController,
    private nav: NavController,
    private modalController: ModalController,
    private utils: UtilsService,
    private nativeStorage: StorageNativeService,
    private app: ApplicationRef,
    private navCtrl: NavController,
  ) { }

  ngOnInit() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      this.nativeStorage.getItem('stockTakeConfirmation').then(v => {
        this.confirmation = v === '1'
      }).catch(() => {})
      this.id = this.routeInfo.snapshot.params.id
      this.init()
      this.loadLocations()
    })
  }
  async init() {
    try {
      const data = await this.relocationService.get(this.id)
      console.log(data)
      this.data = data
    } catch (e) {
      console.error(e)
    }
  }

  async loadLocations() {
    try {
      const res = await this.relocationService.getLocation()
      this.locations = res
    } catch (e) {
      console.error(e)
    }
  }
  getLocation(list, no) {
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.no === no) {
        return item
      }
      if (item.child && item.child.length > 0) {
        const child = this.getLocation(item.child, no)
        if (child) {
          return child
        }
      }
    }
    return null
  }
  getLocationName(no) {
    const item = this.getLocation(this.locations, no)
    return item ? item.location_name : ''
  }

  get title() {
    if (this.data && this.data.asset_check_no) {
      return 'Note No:' + this.data.asset_check_no
    }
    return ''
  }

  get onLine() {
    return navigator.onLine
  }
  onNoteList() {
    this.nav.pop()
  }
  onSave() {
    this.saveData()
  }

  saveData() {
    const id = this.data.id
    const change_num = this.data.change_num
    const relocation_location_no = this.data.relocation_location_no
    const property_relocation_remark = this.data.property_relocation_remark
    if (!change_num || change_num < 0) {
      this.utils.showToast('Please Fill Qty.')
      return
    }
    if (!relocation_location_no) {
      this.utils.showToast('Please choose new location')
      return
    }
    this.relocationService.update({
      id, change_num, relocation_location_no, property_relocation_remark
    }).then(() => {
      this.eventService.emit('update_relocation_list')
      this.nav.pop()
    })
  }

  async onSelectLocation() {
    let list: unknown = []
    try {
      list = await this.relocationService.getLocation()
    } catch (e) {
      console.error(e)
    }
    const res: any = await this.utils.showModal(TreeSelectPage, {
      list,
      title: 'Choose Location',
      codeKey: 'no',
      nameKey: 'location_name',
    })
    console.log(res)
    if (res && res.data && res.data.save) {
      const data = res.data.data
      this.data.relocation_location_no = data.no
      this.data.relocation_location_name = data.location_name
      this.app.tick()
    }
  }

  async searchItem() {
    if (!this.onLine) {
      this.utils.showToast('Please check WIFI connection')
      return
    }
    const codeno = this.data.property_barcode
    await this.loading.start()
    let data = null
    let itemurl = ''
    try {
      data = await this.asset.getItemUrl(codeno)
      itemurl = data.itemurl
      console.log(data)
    } catch (err) {
      console.error(err)
      this.utils.showMsg('Barcode No. not Found')
      await this.loading.end()
      return
    }
    if (itemurl) {
      this.openItemDetail(codeno, itemurl)

    }

    await this.loading.end()
  }

  openItemDetail(codeno, itemurl) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['/item-detail/item'],
      {
        queryParams: {
          codeno,
          url: itemurl
        },
      }
    )
  }
}
