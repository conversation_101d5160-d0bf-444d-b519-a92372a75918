import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ScannerPagePage } from './scanner-page.page';

describe('ScannerPagePage', () => {
  let component: ScannerPagePage;
  let fixture: ComponentFixture<ScannerPagePage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ScannerPagePage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ScannerPagePage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
