import {ApplicationRef, Component, OnInit} from '@angular/core'
import {Platform, AlertController, NavController, ModalController} from '@ionic/angular'
import {ActivatedRoute, Router} from '@angular/router'
import {UtilsService} from '@app/utils/utils.service'
import {TreeSelectPage} from '@app/components/tree-select/tree-select.page'
import {Camera, CameraOptions} from '@ionic-native/camera/ngx'
import {RelocationService} from '@services/sql/relocation.service'
import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import {EventService} from '@services/event/event.service'
import {AssetService} from '@services/api/asset.service'
import {LoadingService} from '@app/utils/loading.service'
import { dateFormat } from '@app/utils/index'

@Component({
  selector: 'app-borrow-item-list',
  templateUrl: './borrow-item-list.page.html',
  styleUrls: ['./borrow-item-list.page.scss'],
})
export class BorrowItemListPage implements OnInit {

  public barcode = ''
  public data: any = {}
  public confirmation = true
  public locations: any = []
  public options: CameraOptions = {
    quality: 50,
    destinationType: this.camera.DestinationType.DATA_URL,
    encodingType: this.camera.EncodingType.JPEG,
    mediaType: this.camera.MediaType.PICTURE,
    // sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
  }
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    private relocationService: RelocationService,
    public asset: AssetService,
    private router: Router,
    private loading: LoadingService,
    private eventService: EventService,
    private alertController: AlertController,
    private nav: NavController,
    private modalController: ModalController,
    private utils: UtilsService,
    private app: ApplicationRef,
    private navCtrl: NavController,
    private camera: Camera,
    private photoViewer: PhotoViewer,
  ) { }

  ngOnInit() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      try {
        this.barcode = this.routeInfo.snapshot.queryParams.barcode
        const data = JSON.parse(this.routeInfo.snapshot.queryParams.data)
        data.change_num = 1
        data.property_borrow_object = '1'
        data.property_borrow_to_name = ''
        data.property_borrow_return_time = ''
        data.property_borrow_remark = ''

        data.old_department_name = data.department_name
        data.old_department_ename = data.department_ename
        data.old_kind_name = data.kind_name
        data.old_kind_ename = data.kind_ename

        data.staff_code = ''
        data.staff_cname = ''
        data.staff_ename = ''

        this.data = data
        console.log(this.barcode, this.data)
      } catch (e) {
        console.error(e)
      }
      this.init()
    })
  }
  async init() {
    try {
      // const data = await this.borrowService.get(this.id)
      // console.log(data)
      // this.data = data
    } catch (e) {
      console.error(e)
    }
  }

  get minDate() {
    return '2000-01-01'
  }
  get maxDate() {
    return dateFormat(new Date(new Date().getFullYear() + 10, 11, 31), 'yyyy-MM-dd')
  }

  get title() {
    if (this.data && this.data.asset_check_no) {
      return 'Note No:' + this.data.asset_check_no
    }
    return ''
  }

  get onLine() {
    return navigator.onLine
  }
  onBackToHome() {
    // this.nav.pop()
    // this.nav.pop().then(() => this.nav.pop());
    this.nav.navigateBack('/function');
  }
  onBack() {
    this.eventService.emit('borrow-clear')
    this.nav.pop()
  }
  onSave() {
    this.saveData()
  }

  async saveData() {
    try {

    } catch (e) {
      console.error(e)
    }
    const property_location_serial_no = this.data.property_location_serial_no
    const property_barcode = this.data.property_barcode
    const property_location = this.data.property_location
    const change_num = this.data.change_num

    let property_borrow_return_time = this.data.property_borrow_return_time
    const property_borrow_remark = this.data.property_borrow_remark
    const property_borrow_object = this.data.property_borrow_object
    const property_borrow_to_name = (Number(property_borrow_object) === 1 ? this.data.staff_code : this.data.property_borrow_to_name)

    if (!property_borrow_to_name) {
      this.utils.showToast('Please choose borrow to staff or other people')
      return
    }
    if (!property_borrow_return_time) {
      this.utils.showToast('Please choose expect return date')
      return
    }
    if (!change_num || change_num < 0) {
      this.utils.showToast('Please Fill Qty.')
      return
    }
    property_borrow_return_time = dateFormat(new Date(property_borrow_return_time), 'yyyy-MM-dd')


    const post_data = {}
    post_data['post_data[0][property_location_serial_no]'] = property_location_serial_no
    post_data['post_data[0][property_location]'] = property_location
    post_data['post_data[0][property_barcode]'] = property_barcode
    post_data['post_data[0][change_num]'] = change_num
    post_data['post_data[0][property_borrow_object]'] = property_borrow_object
    post_data['post_data[0][property_borrow_to_name]'] = property_borrow_to_name
    post_data['post_data[0][property_borrow_return_time]'] = property_borrow_return_time
    post_data['post_data[0][property_borrow_remark]'] = property_borrow_remark
    this.asset.createBorrow(post_data).then(async (res: any) => {
      console.log(res)
      if (res && res.status === 200) {
        // await this.utils.showMsg('提交成功！')
        const alert = await this.alertController.create({
          header: 'Tips',
          message: 'Upload successful!',
          backdropDismiss: false,
          buttons: [
            {
              text: 'Back',
              role: 'cancel',
              cssClass: 'secondary',
              handler: () => {
                this.onBackToHome()
              }
            }, {
              text: 'Next',
              handler: (data) => {
                this.onBack()
              }
            }
          ]
        });
        await alert.present();
        await alert.onDidDismiss()
      } else {
        console.error('失敗')
        return Promise.reject(res)
      }
      // this.eventService.emit('update_borrow_list')
      // this.nav.pop()
    }).catch((err: any) => {
      console.error(err)
      if (err) {
        if (err.msg && typeof err.msg === 'string') {
          this.utils.showMsg(err.msg)
          return
        }
      }
      this.utils.showMsg('Submit failed')
    })
  }

  // 選擇職員
  async onSelectStaff() {
    let list: unknown = []
    try {
      list = await this.asset.getAppStaffs()
    } catch (e) {
      console.error(e)
    }
    const res: any = await this.utils.showModal(TreeSelectPage, {
      list,
      title: 'Select Staff',
      codeKey: 'staff_code',
      nameKey: 'staff_cname',
    })
    console.log(res)
    if (res && res.data && res.data.save) {
      const data = res.data.data
      this.data.staff_code = data.staff_code
      this.data.staff_cname = data.staff_cname
      this.data.staff_ename = data.staff_ename
      this.app.tick()
    }
  }

  async searchItem() {
    if (!this.onLine) {
      this.utils.showToast('Please check WIFI connection')
      return
    }
    const codeno = this.data.property_barcode
    await this.loading.start()
    let data = null
    let itemurl = ''
    try {
      data = await this.asset.getItemUrl(codeno)
      itemurl = data.itemurl
      console.log(data)
    } catch (err) {
      console.error(err)
      this.utils.showMsg('Barcode No. not Found')
      await this.loading.end()
      return
    }
    if (itemurl) {
      this.openItemDetail(codeno, itemurl)

    }

    await this.loading.end()
  }

  openItemDetail(codeno, itemurl) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['/item-detail/item'],
      {
        queryParams: {
          codeno,
          url: itemurl
        },
      }
    )
  }

  isModalOpen = false
  onClickExpectReturnDate() {
    console.log('onClickExpectReturnDate')
    this.isModalOpen = true
  }
  onBlurExpectReturnDate () {
    console.log('onBlurExpectReturnDate')
    this.isModalOpen = false
  }
  get propertyBorrowReturnTimeClassName () {
    let name = 'date-btn-text'
    if (!this.data.property_borrow_return_time) {
      name += ' empty'
    }
    return name
  }
  get propertyBorrowReturnTime () {
    if (this.data.property_borrow_return_time) {
      return dateFormat(new Date(this.data.property_borrow_return_time))
    }
    return 'YYYY-MM-DD'
  }
  onCancelActExpireDate() {
    console.log('onCancelActExpireDate')
    this.data.property_borrow_return_time = undefined
  }
}
