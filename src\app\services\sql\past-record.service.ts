import { Injectable } from '@angular/core';
import {SqlService} from '@services/sql/sql.service'
import { dateFormat} from '../../../utils'

@Injectable({
  providedIn: 'root'
})
export class PastRecordService {

  constructor(
    private sql: SqlService,
    ) {
    this.sql.init()
  }
  get isInit() {
    return this.sql.isInit
  }

  all() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_past_records order by id desc', [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              console.log(item.id, item.code, item.item_name, item.search_time)
              arr.push({
                id: item.id,
                code: item.code,
                item_name: item.item_name,
                search_time: item.search_time,
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  add(code, name) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'INSERT INTO tbl_past_records (code, item_name, search_time) VALUES (?,?,?)',
          [code, name, now],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
}
