ion-content {
  --background: var(--content-page-background);
}
.content {
  height: 100%;
  width: 100%;
  background-size: 100% auto;
  background-position: center;
  background-repeat: no-repeat;
  background-color: var(--content-page-background);
  //background-image: url("/assets/images/index.png");
  padding-top: env(safe-area-inset-top);
  padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
  padding-bottom: env(safe-area-inset-bottom); /* 在 iphone x + 中本句才会生效 */
  padding-right: env(safe-area-inset-right);
  padding-left: env(safe-area-inset-left);

  text-align: center;


  .sch-info {
    height: 50%;
    width: 100%;
    position: relative;
    padding-top: 10%;
    padding-top: calc(var(--ion-safe-area-top) + 10%);
    .sch-logo {
      width: auto;
      height: 30%;
      opacity: 0;
      &.sch-logo-loaded {
        opacity: 1;
      }
    }
    .sch-name-cn {
      font-size: 1rem;
      padding-top: 5px;
    }
    .sch-name-en {
      font-size: 0.8rem;
    }
  }

  .asset-info {
    height: 50%;
    width: 100%;
    position: relative;
    .sys-logo {
      //width: 33%;
      //height: auto;
      height: 25%;
      width: auto;
      margin: 0 auto;
      padding-bottom: 0.5rem;
    }

    .version {
      color: #707070;
      font-size: 0.7rem;
      height: 20%;
      //padding-bottom: 1rem;
    }
    .btn-group {
      text-align: center;
      font-size: 1rem;
      .left {
        display: inline;
        width: 50%;
        padding-right: 5%;
      }
      .right {
        display: inline;
        width: 50%;
        padding-left: 5%;
      }
    }

    .app-footer {
      position: absolute;
      bottom: 0.5rem;
      text-align: center;
      width: 100%;
      font-size: 0.6rem;
    }
  }
}
