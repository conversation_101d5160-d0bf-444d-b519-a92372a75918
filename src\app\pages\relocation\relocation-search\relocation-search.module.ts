import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { RelocationSearchPage } from './relocation-search.page';
import { CardModule } from '@app/components/card/card.module'
// const routes: Routes = [
//   {
//     path: '',
//     component: RelocationSearchPage
//   }
// ];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes),
    CardModule,
  ],
  // declarations: [RelocationSearchPage]
})
export class RelocationSearchPageModule {}
