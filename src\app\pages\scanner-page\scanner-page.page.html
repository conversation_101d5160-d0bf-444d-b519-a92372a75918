<!--<ion-header >-->

<!--&lt;!&ndash;  <ion-navbar >&ndash;&gt;-->

<!--    <ion-title>扫描中...</ion-title>-->

<!--&lt;!&ndash;  </ion-navbar>&ndash;&gt;-->

<!--</ion-header>-->

<!--<ion-content padding >-->

<!--  <div [ngClass]="{'qrscanner-area':isShow}">-->

<!--  </div>-->

<!--  <div  [ngClass]="{'through-line':isShow}"></div>-->

<!--  <div class="button-bottom">-->
<!--    on-navbar-->
<!--    <button (click)="toggleLight()" ion-fab class="icon-camera" margin-right>-->

<!--      <ion-icon name="flash"></ion-icon>-->

<!--    </button>-->

<!--    <button (click)="toggleCamera()" ion-fab class="icon-camera">-->

<!--      <ion-icon name="reverse-camera"></ion-icon>-->

<!--    </button>-->

<!--  </div>-->

<!--</ion-content>-->


<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button  (click)="handleClose()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>
      {{ title }}
    </ion-title>
  </ion-toolbar>
</ion-header>
<div style="background: none transparent;" class="scanner-content content">
  <ion-text class="tips">{{ tips }}</ion-text>
<!--  <div [ngClass]="{'through-line':isShow}"></div>-->
<!--  <img class="scan-area" src="assets/images/qrscanner.png"/>-->
  <div class="scan-box"></div>
  <div *ngIf="showConfirm" class="confirm-area">
    <ion-text class="confirm-label">{{ 'SCANNER.DOUBLE_CONFIRM' | translate }}</ion-text>
    <ion-toggle class="confirm-value" [(ngModel)]="needConfirm"></ion-toggle>
  </div>
</div>
