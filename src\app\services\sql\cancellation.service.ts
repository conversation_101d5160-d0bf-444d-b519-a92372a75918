import { Injectable } from '@angular/core';
import {SqlService} from '@services/sql/sql.service'

@Injectable({
  providedIn: 'root'
})
export class CancellationService {

  public tableName = 'tbl_cancellation'
  constructor(
    private sql: SqlService,
    ) {
    this.sql.init()
  }
  get isInit() {
    return this.sql.isInit
  }

  /* 獲取本地轉移列表 */
  all() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(`SELECT * FROM ${this.tableName} order by id desc`, [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              try {
                item.location_list = JSON.parse(item.location_list)
              } catch (e) {
                console.error(e)
              }
              arr.push({
                ...item
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  /* 增加獲取本地轉移 */
  // tslint:disable-next-line:max-line-length
  add({
        property_location_serial_no, property_barcode, property_location,
        change_num, img, property_status_change_woreason, property_status_change_remark,
        location_list,
        property_no, property_ename, property_name, property_status,
      }: any) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          // tslint:disable-next-line:max-line-length
          `INSERT INTO ${this.tableName} (property_location_serial_no, property_barcode, property_location,` +
          'change_num, img, property_status_change_woreason, property_status_change_remark,' +
          'location_list,' +
          'property_no, property_ename, property_name, property_status' +
          ') VALUES (?,?,?,?,?,?,?,?,?,?,?,?)',
          // tslint:disable-next-line:max-line-length
          [property_location_serial_no, property_barcode, property_location,
            change_num, img, property_status_change_woreason, property_status_change_remark,
            JSON.stringify(location_list),
            property_no, property_ename, property_name, property_status
          ],
          (x, rs) => {
            console.log(x, rs)
            this.all().then(arr => {
              resolve(rs.insertId)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }

  get(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(`SELECT * FROM ${this.tableName} WHERE id=(?)`, [id],
          (x, rs) => {
            if (rs && rs.rows && rs.rows.length > 0 ) {
              const item = rs.rows.item(0)
              console.log('sql row:', item)
              try {
                item.location_list = JSON.parse(item.location_list)
              } catch (e) {
                console.error(e)
              }
              const data = {
                ...item
              }
              resolve(data)
            } else {
              reject(rs.rows)
            }
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  delete(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(
          `DELETE FROM ${this.tableName} WHERE id=(?)`,
          [id],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  // tslint:disable-next-line:max-line-length
  update({ id, property_location_serial_no, property_barcode, property_location,
           change_num, img, property_status_change_woreason, property_status_change_remark,
           location_list,
           property_no, property_ename, property_name, property_status}: any) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        const values = []
        let sqlStr = `UPDATE ${this.tableName} SET `
        const valueStr = []

        // tslint:disable-next-line:max-line-length
        if (property_location_serial_no !== undefined) { valueStr.push('property_location_serial_no=(?)'); values.push(property_location_serial_no); }
        if (property_barcode !== undefined) { valueStr.push('property_barcode=(?)'); values.push(property_barcode); }
        if (property_location !== undefined) { valueStr.push('property_location=(?)'); values.push(property_location); }
        if (change_num !== undefined) { valueStr.push('change_num=(?)'); values.push(change_num); }
        if (img !== undefined) { valueStr.push('img=(?)'); values.push(img); }
        // tslint:disable-next-line:max-line-length
        if (property_status_change_woreason !== undefined) { valueStr.push('property_status_change_woreason=(?)'); values.push(property_status_change_woreason); }
        // tslint:disable-next-line:max-line-length
        if (property_status_change_remark !== undefined) { valueStr.push('property_status_change_remark=(?)'); values.push(property_status_change_remark); }
        if (property_no !== undefined) { valueStr.push('property_no=(?)'); values.push(property_no); }
        if (property_ename !== undefined) { valueStr.push('property_ename=(?)'); values.push(property_ename); }
        if (property_name !== undefined) { valueStr.push('property_name=(?)'); values.push(property_name); }
        if (property_status !== undefined) { valueStr.push('property_status=(?)'); values.push(property_status); }
        if (location_list !== undefined) { valueStr.push('location_list=(?)'); values.push(JSON.stringify(location_list)); }
        if (valueStr.length < 1) {
          reject()
          return
        }
        sqlStr += valueStr.join(', ')
        sqlStr += ' WHERE id=(?)'
        values.push(id)
        tx.executeSql(
          sqlStr,
          values,
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  clear() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(
          `DELETE FROM ${this.tableName}`,
          [],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
}
