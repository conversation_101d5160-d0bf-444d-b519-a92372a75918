import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InventoryItemListPage } from './inventory-item-list.page';

describe('InventoryItemListPage', () => {
  let component: InventoryItemListPage;
  let fixture: ComponentFixture<InventoryItemListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InventoryItemListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InventoryItemListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
