ion-content {
  --background: var(--content-page-background);
  --padding-top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top) + 0.5rem);
  --ppadding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
  --ppadding-bottom: env(safe-area-inset-bottom); /* 在 iphone x + 中本句才会生效 */
  --ppadding-left: calc(env(safe-area-inset-left) + 0.5rem);
  --ppadding-right: calc(env(safe-area-inset-right) + 0.5rem);
}
.content {
  padding: 0.5rem;
  padding-top: 3rem;
  padding-top: 0.5rem;
  background: var(--content-page-background);
  height: 100%;
  width: 100%;
  position: relative;

  .back-btn {
    position: absolute;
    text-align: right;
    top: 1.5rem;
    top: env(safe-area-inset-top);
    top: 0;
    right: 0.5rem;
  }
  app-card {
    ::ng-deep {
      .asset-card {
        margin-top: 1.8rem;
      }
    }
    .header {

      height: 1.5rem;
      vertical-align: middle;

      .label {

        height: 1.5rem;
        line-height: 1.5rem;
      }

      ion-button {
        height: 1.5rem;
        vertical-align: middle;
        float: right;
        margin: 0;
        display: block;

        i {
          font-size: 1rem;
        }
      }
    }
    .body {
      display: inline-grid;

      .record-input {
        display: inline-flex;
        height: 1.7rem;
      }
      .record-btn {
        width: 10%;
        display: inline-grid;
        height: 1.7rem;
        vertical-align: bottom;
        margin: 0 0.1rem;
        --padding-start: 1rem;
        --padding-end: 1rem;
        width: 2.8rem;
        i {
          font-size: 1rem;
        }
      }
    }
  }
}
