import {Component, OnInit} from '@angular/core'
import {UtilsService} from '@app/utils/utils.service'
import {AssetService} from '@services/api/asset.service'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ModalController, NavController} from '@ionic/angular'
import {RelocationSearchPage} from '@pages/relocation/relocation-search/relocation-search.page'
import {NoteListService} from '@services/sql/note-list.service'
import {Router} from '@angular/router'
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import {RelocationService} from '@services/sql/relocation.service'
import { ApplicationRef } from '@angular/core';
import {EventService} from '@services/event/event.service'
import {LoadingService} from '@app/utils/loading.service'

@Component({
  selector: 'app-relocation-list',
  templateUrl: './relocation-list.page.html',
  styleUrls: ['./relocation-list.page.scss'],
})
export class RelocationListPage implements OnInit {

  public locations: any = []
  public list: any = []
  public allCheck = false
  public isInit = false
  constructor(
    private utils: UtilsService,
    private asset: AssetService,
    private modalController: ModalController,
    private nav: NavController,
    private relocationService: RelocationService,
    private router: Router,
    private nativeStorage: StorageNativeService,
    private eventService: EventService,
    private loading: LoadingService,
    private app: ApplicationRef,
  ) { }

  async ngOnInit() {
    // this.hide()
    // await this.relocationService.clear()
    await this.loadData()
    this.loadLocations()
    // this.onAdd()
    this.eventService.event.on('update_relocation_list', () => {
      this.loadData()
      this.loadLocations()
    })
  }
  onBack() {
    this.nav.pop()
  }
  hide() {
    // @ts-ignore
    document.getElementsByTagName('app-relocation-list')[0].style.display = 'none'
  }
  show() {
    setTimeout(() => {
      // @ts-ignore
      document.getElementsByTagName('app-relocation-list')[0].style.display = ''
    }, 1500)
  }
  get onLine() {
    return navigator.onLine
  }

  async loadData() {
    await this.loading.start()
    this.list = []
    this.relocationService.all().then(res => {
      console.log(res)
      this.list = res
      this.app.tick();
    }).catch(err => {
      console.error(err)
    }).finally(() => {
      this.loading.end()
    })
  }
  async onAdd() {
    const data: any = await this.showSearchModal({})
    if (data && data.text) {
      this.isInit = true
      let isExist = false
      let insertId: any = ''
      if (data.property_no) {
        // this.list.push(data)
        try {
          // const item = await this.asset.getAppRelocationInfo(data.text)
          // if (item) {
            isExist = true
          // }
        } catch (e) {
          console.error(e)
        }
        this.show()
        // if (isExist) {
        //   return
        // }
        try {
          insertId = await this.relocationService.add({
              property_barcode: data.text,
              ...data,

              change_num: 1,
              // property_name: '',
              // property_no: '',
              // department_name: '',
              // department_ename: '',
              // kind_name: '',
              // kind_ename: '',
              // serial_no: '',
              // property_maint_fmdate: '',
              // property_maint_todate: '',
              // supplier_name: '',
              // property_status: '',
              // location_name: '',
              // location_ename: '',
              // property_location_serial_no: '',
              // property_location_serial_ctime: '',
              //
              // property_location: '',
              // change_num: '',
              // relocation_location_no: '',
              // property_relocation_remark: ''
          })
        } catch (e) {
          console.error(e)
        }
      } else {
        try {
          insertId = await this.relocationService.add({
            property_barcode: data.text,
            property_name: '',
            property_no: '',
            department_name: '',
            department_ename: '',
            kind_name: '',
            kind_ename: '',
            serial_no: '',
            property_maint_fmdate: '',
            property_maint_todate: '',
            supplier_name: '',
            property_status: '',
            location_name: '',
            location_ename: '',
            property_location_serial_no: '',
            property_location_serial_ctime: '',

            property_location: '',
            change_num: 1,
            relocation_location_no: '',
            property_relocation_remark: ''
          })
        } catch (e) {
          console.error(e)
        }
      }
      if (insertId) {
        this.openItem(insertId)
      }
    } else {
      // if (!this.isInit) {
      //   this.onBack()
      // } else {
        this.show()
      // }
    }
    if (this.isInit) {
      this.show()
      await this.loadData()
    }
  }


  async checkNo(checkno) {
    try {
      const res = await this.asset.checkListNo(checkno)
      return true
    } catch (e) {
      console.error(e)
    }
    return false
  }


  async showSearchModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: RelocationSearchPage,
      componentProps: {
        ...props,
        id: 'relocation-search-modal'
      },
      id: 'relocation-search-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data && res.data.save) {
      if (res.data.info) {
        return {
          ...res.data.info,
          list: res.data.list,
          text: res.data.text
        }
      } else {
        return {
          text: res.data.text
        }
      }
    }
    return {}
  }

  onDelete(item) {
    console.log(item)
    this.relocationService.delete(item.id)
    // const i = this.list.findIndex(i => i.id === item.id )
    this.loadData()
  }
  onClick(item) {
    console.log(item)
    this.openItem(item.id)
  }
  openItem(id) {
    this.nav.setDirection('forward');
    this.router.navigate(
      ['relocation-item-list', id],
    )
  }

  async onUpload() {
    if (!await this.utils.confirm('Warning', 'Confirm the upload?', 'CONFIRM', 'CANCEL')) {
      return
    }
    if (!this.onLine) {
      this.utils.showMsg('Please check WIFI connection')
    }
    const list = this.list // .filter(i => i.check)
    if (list.length > 0) {
      console.log(list)
      let user_id = ''
      try {
        user_id = await this.nativeStorage.getItem('user_id')
      } catch (e) {
        console.log('無user_id')
        this.utils.showMsg('Timeout, Please re-login')
        return
      }
      await this.loading.start()
      const data = list.map(item => {
        return {
          id: item.id,
          property_location_serial_no: item.property_location_serial_no,
          property_barcode: item.property_barcode,
          property_location: item.property_location,
          change_num: item.change_num,
          relocation_location_no: item.relocation_location_no,
          property_relocation_remark: item.property_relocation_remark,
        }
      })
      const post_data = {}
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        post_data['post_data[' + i + '][id]'] = item.id
        post_data['post_data[' + i + '][property_location_serial_no]'] = item.property_location_serial_no
        post_data['post_data[' + i + '][property_barcode]'] = item.property_barcode
        post_data['post_data[' + i + '][property_location]'] = item.property_location
        post_data['post_data[' + i + '][change_num]'] = item.change_num
        post_data['post_data[' + i + '][relocation_location_no]'] = item.relocation_location_no
        post_data['post_data[' + i + '][property_relocation_remark]'] = item.property_relocation_remark || ''
      }
      try {
        const res: any = await this.asset.checkRelocation(post_data)
        console.log(res)
        const isSuccess = res.data.every(i => i.property_status === 0)
        debugger
        if (isSuccess) {
          const res2 = await this.asset.createRelocation(post_data)
          console.log(res2)
          for (const item of list) {
            await this.relocationService.delete(item.id)
          }
          this.utils.showToast('Upload complete!')
        } else {
          const result: any = res.data
          // tslint:disable-next-line:prefer-for-of
          for (let i = 0; i < result.length; i++) {
            const item = result[i]
            this.relocationService.update({
              id: item.id,
              property_no: item.property_no,
              property_ename: item.property_ename,
              property_name: item.property_name,
              property_status: item.property_status,
              location_list: item.location_list,
            })
          }
          this.utils.showToast('Upload failed!')
        }

        for (const item of list) {
          await this.relocationService.delete(item.id)
        }
        this.utils.showToast('Upload complete!')
      } catch (e) {
        console.error(e)
        this.utils.showToast('Upload failed!')
      }
      this.loading.end()
      this.loadData()
    }
  }
  onCheckAll() {

    // const check = this.list.event(i => i.check)
    this.list.forEach(i => {
      i.check = this.allCheck
    })
  }
  onChangeItem(item) {
    this.allCheck = this.list.every(i => i.check)
  }
  getLocationName(no) {
    const item = this.relocationService.getLocationItem(this.locations, no)
    return item ? item.location_name : ''
  }
  async loadLocations() {
    try {
      const res = await this.relocationService.getLocation()
      this.locations = res
    } catch (e) {
      console.error(e)
    }
  }

  getStatus(item) {
    switch (item.property_status) {
      case 0:
        return 'Normal'
      case 1:
        return 'Same Location'
      case 2:
        if (item.location_list && item.location_list.length > 0) {
          return 'Invalid Barcode No. in Location'
        } else {
          return 'Barcode No. not Found'
        }
      case 3:
        return 'Invalid Qty.'
      default:
        return 'Not Approval'
    }
  }
  getStatusColor(item) {
    switch (item.property_status) {
      case 0:
        return 'success'
      case 1:
        return 'warning'
      case 2:
        return 'danger'
      case 3:
        return 'warning'
      default:
        return ''
    }
  }
}
