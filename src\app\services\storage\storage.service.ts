import {Injectable} from '@angular/core'
import {IsDebug} from '@ionic-native/is-debug/ngx'
import {Platform} from '@ionic/angular'

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  public appInfo = {
    poweredBy: 'Norray Professional Computer Ltd.',
    hotline: '2394-4114',
  }
  public serverSetting = {
    // http: 'http',
    // port: '80',
    serverName: '',
    ip: '',
    realHost: '',
    projectRoot: '',
    remoteProjectName: '',

    uri: 'web',
  }

  public store: any = {
    language: '',
    device: '',
    school: {
      school_id: '',
      school_name_cn: '',
      school_name_en: '',
      school_code: '',
      school_pre: '',
      show_grade_pre: 0,
      school_pre_array: [],
      grade_range_array: [],
      address: '',
      school_logo: '',
      app_logo: '',
      graduate_grade: '',
      language: []
    },
    // USER
    userId: '',
    avatar: '',
    name: '',
    name_cn: '',
    name_en: '',
    roles: [],
    login_role: '',
    web_code: '',
    reader_code: '',
  }

  constructor(
    private isDebug: IsDebug,
    private platform: Platform
  ) {
    sessionStorage.setItem('language', 'zh-hk')
    // sessionStorage.setItem('device', 'mobile')
    sessionStorage.setItem('web_code', 'LB')
    this.platform.ready().then(() => {
      const platforms = this.platform.platforms()
      let device = 'Web'
      if (platforms.includes('android')) {
        device = 'Android'
      } else if (platforms.includes('ios')) {
        device = 'iOS'
      }
      // device = 'Web'
      sessionStorage.setItem('device', device)


      // this.serverSetting.serverName = this.serverSetting.ip = '*************'
      // this.serverSetting.projectRoot = this.serverSetting.remoteProjectName = 'EDSchoolCenter'
      this.isDebug.getIsDebug().then(isDebugging => {
        if (isDebugging) {
          // this.serverSetting.serverName = this.serverSetting.ip = '*************'
          // this.serverSetting.projectRoot = this.serverSetting.remoteProjectName = 'EDSchoolCenter'
        }
      })
    })
    // this.serverSetting.serverName = this.serverSetting.ip = 'test.esaccount.com'
    // this.serverSetting.projectRoot = this.serverSetting.remoteProjectName = 'EDSchoolCenter-LB'


    // setInterval(() => {
    //   this.store.school.time = new Date().getTime()
    // }, 100)
  }

  set(key: any, value: any) {
    if (this.store.hasOwnProperty(key)) {
      this.store[key] = value
    }
    if (typeof value === 'object') {
      sessionStorage.setItem(key + '_object', '1')
      return sessionStorage.setItem(key, JSON.stringify(value))
    } else {
      sessionStorage.removeItem(key + '_object')
      return sessionStorage.setItem(key, value)
    }

  }

  get() {
    for (const key in this.store) {
      if (this.store.hasOwnProperty(key)) {
        if (sessionStorage.getItem(key + '_object') === '1') {
          this.store[key] = JSON.parse(sessionStorage.getItem(key))
        } else {
          this.store[key] = sessionStorage.getItem(key)
        }
      }
    }

    return this.store
  }
}
