import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { BorrowItemListPage } from './borrow-item-list.page';

describe('BorrowItemListPage', () => {
  let component: BorrowItemListPage;
  let fixture: ComponentFixture<BorrowItemListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BorrowItemListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BorrowItemListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
