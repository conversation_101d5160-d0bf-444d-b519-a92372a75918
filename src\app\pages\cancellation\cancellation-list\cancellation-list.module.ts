import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { CancellationListPage } from './cancellation-list.page';

import { EmptyModule } from '@app/components/empty/empty.module'

const routes: Routes = [
  {
    path: '',
    component: CancellationListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    EmptyModule,
  ],
  declarations: [CancellationListPage]
})
export class CancellationListPageModule {}
