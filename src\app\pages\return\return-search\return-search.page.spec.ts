import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReturnSearchPage } from './return-search.page';

describe('ReturnSearchPage', () => {
  let component: ReturnSearchPage;
  let fixture: ComponentFixture<ReturnSearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReturnSearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReturnSearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
