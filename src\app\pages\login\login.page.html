<form (submit)="handleLogin()" class="login-page">
  <ion-content fullscreen>
    <div class="content">
      <div class="sys-info">
        <ion-img src="assets/images/logo.png" class="sys-logo"></ion-img>
        <div class="sys-name">資產管理系統</div>
      </div>
      <div class="back-btn">
        <ion-button
            color="asset"
            style=""
            (click)="onBack()">Back</ion-button>
      </div>
      <div class="login">
        <!-- Company -->
        <app-card>
          <span class="header">Login</span>
          <div class="body">
            <span class="label" >Username</span>
            <ion-input
                [(ngModel)]="loginForm['username']"
                type="text"
                name="username"
                clear-input
                (keyup.enter)="handleNextInput()"
            ></ion-input>
            <span class="label" >Password</span>
            <ion-input
                #pwd
                [(ngModel)]="loginForm['password']"
                type="password"
                name="password"
                clear-input
                (keyup.enter)="onSubmit()"
            ></ion-input>
            <div class="actions">
              <ion-button [disabled]="!canSubmit" color="asset" (click)="onSubmit()">Login</ion-button>
            </div>
          </div>
        </app-card>
        <div class="app-footer">
          Powered By : {{ poweredBy }}
          <br> Hotline : {{ hotline }}
        </div>
      </div>
    </div>
  </ion-content>

</form>
