import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import {TranslateModule} from '@ngx-translate/core'
import { LoginPage } from './login.page';
import { CardModule } from '@app/components/card/card.module'

const routes: Routes = [
  {
    path: '',
    component: LoginPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    TranslateModule,
    CardModule,
  ],
  declarations: [LoginPage]
})
export class LoginPageModule {}
