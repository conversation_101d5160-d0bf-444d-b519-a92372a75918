<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="onCancel()">
        Cancel
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="onSave()">
        Confirm
      </ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-item *ngIf="isChild" lines="full" [detail]="false" button (click)="onBackParent()">
      <ion-label> ... </ion-label>
      <!--<ion-icon *ngIf="item.isParent === 'false'" icon="radio-button-off" slot="end"></ion-icon>-->
    </ion-item>
    <ion-item *ngFor="let item of currentList" lines="full" [detail]="item.isParent === 'true'" >
      <ion-label (click)="onClickItem(item)">{{ item[nameKey] }}</ion-label>
      <!--<ion-icon *ngIf="item.isParent === 'false'" icon="radio-button-off" slot="end"></ion-icon>-->
      <!--<ion-checkbox [(ngModel)]="item.check" slot="end" (ngModelChange)="onChangeItem(item)"></ion-checkbox>-->
      <span class="checkbox-container">
        <ion-checkbox [(ngModel)]="item.check" slot="end"  (ngModelChange)="onChangeItem(item)"></ion-checkbox>
      </span>
    </ion-item>
  </ion-list>

</ion-content>
