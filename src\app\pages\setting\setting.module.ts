import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { SettingPage } from './setting.page';
import { CardModule } from '@app/components/card/card.module'
import { FooterModule } from '@app/components/footer/footer.module'
import {TranslateModule} from '@ngx-translate/core'

const routes: Routes = [
  {
    path: '',
    component: SettingPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    CardModule,
    FooterModule,
    TranslateModule
  ],
  declarations: [SettingPage]
})
export class SettingPageModule {}
