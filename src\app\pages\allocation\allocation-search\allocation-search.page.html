<ion-content>
  <div class="content">
    <div class="back-btn">
      <ion-button
          color="asset"
          style="font-size: 0.7rem;height: 1.5rem;"
          (click)="onBack()">Back</ion-button>
    </div>

    <app-card>
      <div class="header">
        <span class="label">Waiting for Allocation Item</span>
        <!--<ion-button color="asset-plain" (click)="onSearchBarCodeNo()">Search</ion-button>-->
        <!--<ion-button color="asset-plain">-->
        <!--  <i slot="icon-only" class="iconfont icon-record-2"></i>-->
        <!--</ion-button>-->
      </div>
      <div class="body" style="width: 100%;">
        <span class="label" >Bar Code No.</span>
        <ion-input
            [(ngModel)]="noteNo"
            type="text"
            class="record-input"
            clear-input></ion-input>

        <!--<ion-button color="asset" class="record-btn" (click)="onRecordMenu()">-->
        <!--  <i slot="icon-only" class="iconfont icon-record-2"></i>-->
        <!--</ion-button>-->
        <div class="actions">
          <ion-button color="asset" (click)="onScan()">Scan</ion-button>
          <ion-button color="asset" (click)="onConfirm()">Confirm</ion-button>
        </div>
      </div>
    </app-card>
  </div>
</ion-content>
