import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RelocationSearchPage } from './relocation-search.page';

describe('RelocationSearchPage', () => {
  let component: RelocationSearchPage;
  let fixture: ComponentFixture<RelocationSearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RelocationSearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RelocationSearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
