

<ion-menu side="end" mode="md" menu-id="past-record" swipe-gesture="false" autoHide="false" contentId="page-content">
  <ion-header>
    <ion-toolbar color="asset" class="menu-toolbar">
      <ion-title>Past Records</ion-title>
      <!--<ion-button slot="end">Done</ion-button>-->
      <ion-buttons slot="end">
        <ion-button (click)="onCloseMenu()">Done</ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content class="ion-padding" id="past-record-content">
    <ion-list *ngIf="records.length > 0">
      <!--<ion-item *ngFor="let item of records">{{ item.item_name }}</ion-item>-->
      <ion-item *ngFor="let item of records" lines="full" button (click)="onItemRecord(item)">
        <ion-label>
          <h2>{{ item.code }}</h2>
          <h3>{{ item.item_name }}</h3>
          <p>{{ item.search_time }}</p>
        </ion-label>
      </ion-item>
    </ion-list>
    <app-empty *ngIf="records.length === 0" [message]="'No past records.'"></app-empty>
  </ion-content>
</ion-menu>
<div class="ion-page" id="page-content">
  <ion-content>
    <div class="content">
      <div class="back-btn">
        <ion-button
            color="asset"
            style="font-size: 0.7rem;height: 1.5rem;"
            (click)="onLogout()">Logout</ion-button>
      </div>
      <app-card class="first-card">
        <div class="header">
          <span class="label">Manual Search</span>
          <!--<ion-button color="asset-plain" (click)="onSearchBarCodeNo()">Search</ion-button>-->
          <!--<ion-button color="asset-plain">-->
          <!--  <i slot="icon-only" class="iconfont icon-record-2"></i>-->
          <!--</ion-button>-->
        </div>
        <div class="body">
          <span class="label" >Bar Code No.</span>
          <ion-input
              [(ngModel)]="barCodeNo"
              type="text"
              class="record-input"
              clear-input></ion-input>

          <ion-button color="asset" class="record-btn" (click)="onRecordMenu()">
            <i slot="icon-only" class="iconfont icon-record-2"></i>
          </ion-button>
          <div class="actions">
            <ion-button color="asset" (click)="onSearchBarCodeNo()">Search</ion-button>
            <ion-button color="asset" (click)="onScanBarCodeNo()">Scan</ion-button>
          </div>
        </div>
      </app-card>
      <!-- 盤點 -->
      <app-card >
        <div class="header">
          <span class="label">Daily Operation</span>
          <!--<ion-button color="asset-plain" (click)="onSearchBarCodeNo()">Search</ion-button>-->
          <!--<ion-button color="asset-plain">-->
          <!--  <i slot="icon-only" class="iconfont icon-record-2"></i>-->
          <!--</ion-button>-->
        </div>
        <div class="body full-btn">
          <!--<span class="label" >Bar Code No.</span>-->

          <!--<ion-input-->
          <!--    [(ngModel)]="barCodeNo"-->
          <!--    type="text"-->
          <!--    class="record-input"-->
          <!--    clear-input></ion-input>-->

          <!--<ion-button color="asset" class="record-btn" (click)="onRecordMenu()">-->
          <!--  <i slot="icon-only" class="iconfont icon-record-2"></i>-->
          <!--</ion-button>-->
          <div class="actions">
            <!--<ion-button color="asset" (click)="in()">In</ion-button>-->
            <!--<ion-button color="asset" (click)="onScanBarCodeNo()">Scan</ion-button>-->
            <ion-button *ngIf="showModule('AssetCheck')" color="asset" (click)="in()">Stock Take</ion-button>
            <ion-button *ngIf="showModule('Cancellation')" color="asset" (click)="cancellation()">W/O Apply</ion-button>
            <ion-button *ngIf="showModule('Relocation')" color="asset" (click)="relocation()">Article Relocation</ion-button>
            <ion-button *ngIf="showModule('PropertyAllocation')" color="asset" (click)="allocation()">Dept./Category Allocation</ion-button>
            <ion-button *ngIf="showModule('Borrow')" color="asset" (click)="borrow()">Borrow</ion-button>
            <ion-button *ngIf="showModule('PropertyReturn')" color="asset" (click)="return()">Return</ion-button>
            <!--<div class="empty-btn">&nbsp;</div>-->
            <!-- {{ hasModule }} -->
          </div>
        </div>
      </app-card>
      <!-- 盤點 -->
      <!--<app-card>-->
      <!--  <div class="header">-->
      <!--    <span class="label">註銷申請</span>-->
      <!--  </div>-->
      <!--  <div class="body">-->
      <!--    <div class="actions">-->
      <!--      <ion-button color="asset" (click)="cancellation()">In</ion-button>-->
      <!--    </div>-->
      <!--  </div>-->
      <!--</app-card>-->
    </div>
  </ion-content>
</div>