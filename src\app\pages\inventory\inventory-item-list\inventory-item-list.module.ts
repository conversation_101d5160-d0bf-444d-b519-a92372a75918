import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { InventoryItemListPage } from './inventory-item-list.page';

const routes: Routes = [
  {
    path: '',
    component: InventoryItemListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [InventoryItemListPage],
  // entryComponents: []
})
export class InventoryItemListPageModule {}
