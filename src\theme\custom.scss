@-webkit-keyframes rightIn {
  from {
    opacity: 0;
    height: 0;
    /* -webkit-transform: scale3d(.3, .3, .3); */
    /* transform: scale3d(.3, .3, .3); */
    transform: translateX(-90%) translateX(0)
  }
  50% {
    opacity: 1;
  }
  to {
    height: auto;
  }
}

@keyframes rightIn {
  from {
    opacity: 0;
    height: 0;
    /* -webkit-transform: scale3d(.3, .3, .3); */
    /* transform: scale3d(.3, .3, .3); */
    transform: translateX(90%) translateX(0)
  }
  50% {
    opacity: 1;
  }
  to {

    height: auto;
  }
}

@-webkit-keyframes rightOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    height: 0;
    opacity: 0;
    transform: translateX(90%) translateX(0)
  }
}

@keyframes rightOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  to {
    height: 0;
    opacity: 0;
    transform: translateX(90%) translateX(0)
  }
}

:root {
  --notch-inset-top: 0px;
  --notch-inset-right: 0px;
  --notch-inset-bottom: 0px;
  --notch-inset-left: 0px;
}

html{
  body {
    --ion-safe-area-top: var(--notch-inset-top);
  }
}

.item-enter {

  -webkit-animation-name: rightIn;
  animation-name: rightIn;
  -webkit-animation-duration: .3s;
  animation-duration: .3s;
  -webkit-animation: rightIn .3s;
  animation: rightIn .3s;
}

.item-leave {
  -webkit-animation-name: rightOut;
  animation-name: rightOut;
  -webkit-animation-duration: .3s;
  animation-duration: .3s;
  -webkit-animation: rightOut .3s;
  animation: rightOut .3s;
}


ion-app.cameraView, ion-app.cameraView ion-content, ion-app.cameraView .nav-decor {
  background: transparent none !important;
}


ion-app {

  .content-item {
    padding: 14px 27px;
    //--padding-start: 27px;
    //--padding-end: 27px;
    //--padding-top: 14px;
    //--padding-bottom: 14px;
  }

  ion-button.button-disabled {
    --background: #B3B3B3;
  }

  .utils-toast {
    //transform: translateY(-56px) !important;
    --ion-safe-area-bottom: 56px;
    --border-color: #e8e8e8;
    --border-width: 1px;
    --border-style: solid;
  }

  .iconfont {
    font-size: 0.4rem;
  }
}

html {
  font-size: 5.33333vw
  //line-height: 0.7rem;
}

@media screen and (max-width: 320px) {
  html {
    font-size:42.667px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 321px) and (max-width:360px) {
  html {
    font-size:48px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 361px) and (max-width:375px) {
  html {
    font-size:50px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 376px) and (max-width:393px) {
  html {
    font-size:52.4px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 394px) and (max-width:412px) {
  html {
    font-size:54.93px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 413px) and (max-width:414px) {
  html {
    font-size:55.2px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 415px) and (max-width:480px) {
  html {
    font-size:64px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 481px) and (max-width:540px) {
  html {
    font-size:72px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 541px) and (max-width:640px) {
  html {
    font-size:85.33px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 641px) and (max-width:720px) {
  html {
    font-size:96px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 721px) and (max-width:768px) {
  html {
    font-size:102.4px;
    font-size: 5.33333vw
  }
}

@media screen and (min-width: 769px) {
  html {
    font-size:102.4px;
    font-size: 5.33333vw
  }
}

body {
  font-family: "PingFangSC-Regular","Microsoft YaHei",Helvetica;
  background: #f5f7f9
}



.action-sheet-title.sc-ion-action-sheet-md {
  height: auto;
}

.empty-tips {
  height: 10em;
  color: #e0e0e0;
  font-size: 0.3em;
  text-align: center;
  line-height: 10em;
}





.ion-color-asset {
  --ion-color-base: #519BD4;
  --ion-color-base-rgb: 81,155,212;
  --ion-color-contrast: #ffffff;
  --ion-color-contrast-rgb: 255,255,255;
  --ion-color-shade: #347AA8;
  --ion-color-tint: #347AA8;
  --padding-start: 2em;
  --padding-end: 2em;
}

.ion-color-asset-plain {
  --ion-color-base: #ffffff;
  --ion-color-base-rgb: 236,236,236;
  --ion-color-contrast: #519bd4;
  --ion-color-contrast-rgb: 149,181,255;
  --ion-color-shade: #f2f2f2;
  --ion-color-tint: #f2f2f2;
  --padding-start: 2em;
  --padding-end: 2em;
}
//.ion-color-asset {
//  --ion-color-base: var(--ion-color-primary, #519BD4) !important;
//  --ion-color-base-rgb: var(--ion-color-primary-rgb, 56, 128, 255) !important;
//  --ion-color-contrast: var(--ion-color-primary-contrast, #fff) !important;
//  --ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 255, 255, 255) !important;
//  --ion-color-shade: var(--ion-color-primary-shade, #347AA8) !important;
//  --ion-color-tint: var(--ion-color-primary-tint, #3796c7) !important;
//}


.app-footer {
  //position: absolute;
  //bottom: 0.5rem;
  background-color: var(--content-page-background);
  text-align: center;
  width: 100%;
  font-size: 0.6rem;
  color: #212121;
  font-size: 0.5rem;
  opacity: 0.45;
}

/* pad 彈窗全屏 */
ion-app {
  ion-modal {
    div.modal-wrapper {
      --width: 100%;
      --height: 100%;
    }
  }
}

ion-router-outlet{
  background: var(--content-page-background);
}

input.native-input {
  --padding-start: 0.5em!important;
}


.placeholder {
  color: #B1B1B1;
  opacity: 1;
}
.sc-ion-input-ios-h, .sc-ion-textarea-ios-h {
  --placeholder-color: #B1B1B1;
  --placeholder-opacity: 1,
}
body {
  padding-top: env(safe-area-inset-top);
  // padding-top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top));
  padding-right: env(safe-area-inset-right);
  padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
  padding-bottom: calc(env(safe-area-inset-bottom) + 50px); /* 在 iphone x + 中本句才会生效 */
  padding-left: env(safe-area-inset-left);
}
.ion-page {
  //padding-bottom: var(--ion-soft-button);
}
app-root > ion-app {
  margin-bottom: var(--ion-soft-button);
}



.show-camera{
  --background: transparent;
}
ion-modal.camera-hidden{
  display: none;
  opacity: 0;
}
