import {Injectable} from '@angular/core'

import {RequestService} from '../request/request.service'

@Injectable({
  providedIn: 'root'
})
export class LoginService {

  constructor(public request: RequestService) {
  }

  logout() {
    return this.request.request({
      url: '/user/logout',
      method: 'post'
    })
  }

  getSchool() {
    return this.request.request({
      url: '/ed/school/info',
      method: 'get'
    })
  }



}
