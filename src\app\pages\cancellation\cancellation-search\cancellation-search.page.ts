import {Component, OnInit} from '@angular/core'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ScannerPagePage} from '@pages/scanner-page/scanner-page.page'
import {ModalController, NavController} from '@ionic/angular'
import {AssetService} from '@services/api/asset.service'
import {UtilsService} from '@app/utils/utils.service'
import {LoadingService} from '@app/utils/loading.service'

@Component({
  selector: 'app-cancellation-search',
  templateUrl: './cancellation-search.page.html',
  styleUrls: ['./cancellation-search.page.scss'],
})
export class CancellationSearchPage implements OnInit {

  public noteNo = ''
  constructor(
    private modalController: ModalController,
    private asset: AssetService,
    private utils: UtilsService,
    private nav: NavController,
    private loading: LoadingService,
  ) { }

  ngOnInit() {
  }
  get onLine() {
    return navigator.onLine
  }
  onBack() {
    this.modalController.dismiss({
      cancel: true
    })
  }
  async onScan() {
    const text = await this.showScanModal({})
    if (text) {
      this.noteNo = text
      this.handleSearch(text)
    }
  }
  async onConfirm() {
    const text = this.noteNo
    this.handleSearch(text)
  }



  async showScanModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ScannerPagePage,
      componentProps: {
        ...props,
        id: 'list-scanner-modal'
      },
      id: 'list-scanner-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    return res.data && res.data.text || ''
  }


  async checkNo(checkno) {
    try {
      return await this.asset.getItemUrl(checkno)
    } catch (e) {
      console.error(e)
      return Promise.reject(e)
    }
    return null
  }
  async handleSearch(text) {
    console.log('handleSearch', text)

    if (typeof text !== 'string' || text.length <= 0) {
      return
    }
    if (!this.onLine) {
      setTimeout(() => {
        this.modalController.dismiss({
          save: true,
          text
        })
      })
      return
    }
    try {
      await this.loading.start()
      const data: any = await this.checkNo(text)
      console.log('checkNo', data)
      if (data) {
        await this.loading.end()
        this.modalController.dismiss({
          save: true,
          text,
          info: data,
        })
      } else {
        throw data
      }
    } catch (e) {
      console.error(e)
      await this.loading.end()
      this.utils.showMsg('Barcode No. not Found')
    }
  }
  async getNoteList(checkno) {
    try {
      return await this.asset.getItemList(checkno)
    } catch (e) {
      console.error(e)
    }
    return false
  }
}
