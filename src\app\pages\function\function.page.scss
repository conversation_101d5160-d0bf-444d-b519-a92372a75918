#page-content {
  background: var(--content-page-background);
}
ion-content {
  --background: var(--content-page-background);
}
.content {
  padding: 0.5rem;
  padding-top: 3rem;
  padding-top: calc(env(safe-area-inset-top) + 1.5rem);
  background: var(--content-page-background);
  height: 100%;
  width: 100%;
  position: relative;
  overflow: auto;

  .back-btn {
    position: absolute;
    text-align: right;
    top: 1.5rem;
    top: env(safe-area-inset-top);
    right: 0.5rem;
  }
  .first-card {
    ::ng-deep {
      .asset-card {
        // margin-top: 1.6rem;
      }
    }
  }
  app-card {
    .header {

      height: 1.5rem;
      vertical-align: middle;

      .label {

        height: 1.5rem;
        line-height: 1.5rem;
      }

      ion-button {
        height: 1.5rem;
        vertical-align: middle;
        float: right;
        margin: 0;
        display: block;

        i {
          font-size: 1rem;
        }
      }
    }
    .body {
      .record-input {
        width: 80%;
        display: inline-flex;
        height: 1.7rem;
        width: calc(100% - 3rem);
        vertical-align: middle;
      }
      .record-btn {
        width: 10%;
        display: inline-grid;
        height: 1.7rem;
        vertical-align: bottom;
        margin: 0 0.1rem;
        --padding-start: 1rem;
        --padding-end: 1rem;
        width: 2.8rem;
        vertical-align: middle;
        i {
          font-size: 1rem;
        }
      }
    }
    .empty-btn {
      width: 5rem;
      margin: 0.5rem 1rem;
      font-size: 0.7rem;
      line-height: 1rem;
      padding: 0rem;
      height: 1.5rem;
      display: inline-block;
      &:after {
        content: " "
      }
    }
    .full-btn {
      .actions {
        text-align: center;

        ion-button {

          width: 80%;
          margin: 0.5rem 1rem;
          font-size: 0.7rem;
          line-height: 1rem;
          padding: 0rem;
          height: 1.5rem;
        }
      }
    }
  }

}

.menu-toolbar {
  --padding-start: 0;
  --padding-end: 0;
}

ion-menu {
  ::ng-deep {
    .menu-content-open {
      pointer-events: auto;
    }
  }
  &[menu-id="past-record"] {
    z-index: 2;
  }
}

#past-record-content {
  //animation-name: none!important;
  animation-name: ion-animation-1!important;
}

#page-content {
  padding-top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top) + 0.5rem);
  padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
  padding-bottom: env(safe-area-inset-bottom); /* 在 iphone x + 中本句才会生效 */
  padding-left: calc(env(safe-area-inset-left) + 0.5rem);
  padding-right: calc(env(safe-area-inset-right) + 0.5rem);
}
