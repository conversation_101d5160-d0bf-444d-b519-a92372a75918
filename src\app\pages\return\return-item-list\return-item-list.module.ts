import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ReturnItemListPage } from './return-item-list.page';

const routes: Routes = [
  {
    path: '',
    component: ReturnItemListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [ReturnItemListPage],
  // entryComponents: []
})
export class ReturnItemListPageModule {}
