import { Directive, ElementRef, OnInit, HostListener, Input  } from '@angular/core';
import {sleep} from '../utils'

@Directive({
  selector: '[appEnter]'
})
export class EnterDirective {
  // @Input() val: any;

  constructor(private el: ElementRef) {
    console.log('appEnter')
    // el.nativeElement.style.backgroundColor = 'yellow';
    this.removeClass()
  }
  async removeClass() {
    await sleep(300)
    // debugger
    this.el.nativeElement.classList.remove('item-enter')
    console.log('remove item-enter')
  }

  OnInit() {
    // debugger
    this.el.nativeElement.style.backgroundColor = 'skyblue';
  }
  // @HostListener('mouseenter') onMouseEnter() {
  //   debugger
  // }
  //
  // @HostListener('mouseleave') onMouseLeave() {
  //   debugger
  // }
}
