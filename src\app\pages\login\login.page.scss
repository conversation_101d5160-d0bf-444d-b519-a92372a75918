ion-content {
  --background: var(--content-page-background);
}
.form_input {
    --padding-start : 8px !important
}

.login-page {
  .login-toolbar {
    --background: none;
  }

  background: white;
  height: 100vh;
  height: calc(100vh - var(--ion-safe-area-top) - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  //padding-top: 5%;

  /* 居中 */
  //display: flex;
  //display: -webkit-flex;
  //align-items: center;
  //justify-content: center;


  .logo-img {
    width: 100px;
    height: 100px;
    margin-left: auto;
    margin-right: auto;
  }

  .setting-btn {
    float: right;
    padding-right: 10%;
    font-size: 21px;
  }

  .sch-name-cn {
    color: #212121;
    font-size: 16px;
    display: block;
    margin-bottom: 5px;
    width: 70%;
    text-align: center;
    margin: auto auto 5px auto;
    //line-height: .8rem;
  }
  .sch-name-en {
    color: #212121;
    font-size: 7px;
    display: block;
    margin-bottom: 5px;
    width: 70%;
    text-align: center;
    margin: auto auto 5px auto;
    //line-height: .8rem;
  }


  .form-item {
    width: 60%;
    margin: auto;
    --background-focused: #ffffff!important;
    .form-input {
      --background: #ffffff;
      text-align: center;
      padding-left: 30px!important;
      font-size: 13px;
      --padding-bottom: 0;
      ::ng-deep {
        input {
          width: 100%;
          margin-right: -30px;
          padding-right: 30px;
        }
      }
    }
  }


  .submit-btn {
    //width: 20%;
    //margin: auto;
    //background-color: #003658;
    --background: #003658;
    --ion-color-tint: #003658 !important;
    --ion-color-shade: #003658 !important;
    --ion-color-base: #003658 !important;
    //background-color: #003658;
    --color: #FFFFFF !important;
    --border-radius: 12px;
    //padding: 6px 92px;
    --padding-top: 6px;
    --padding-bottom: 6px;
    --padding-start: 92px;
    --padding-end: 92px;
    font-size: 13px;

    //width: 60%;
    ::ng-deep {
      button {
        //padding: 6px 92px;
      }
    }
  }
  .submit-btn.button-disabled {
    --background: #979797!important;
  }

  .forget-password {
    text-decoration: none;
    color: #212121;
    font-size: 10px;

  }
  .login-page-footer {

    flex: 0.2;
    //padding: 10px 0;
    color: #212121;
    font-size: 11px;
    opacity: 0.45;
    text-align: center;
  }

  .content {
    //display: flex;
    //flex-direction: column;
    height: 100%;
    //background-image: url("/assets/images/loginBg.png");
    background-repeat: no-repeat;
    background-size: 100% auto;
    //background-position-y: 92%;
    background-position-x: left;

    background-position-y: 100%;

    padding-top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top));
    padding-bottom: 50px;  /* 兼容不支持 env( ) 的设备  */
    padding-bottom: env(safe-area-inset-bottom); /* 在 iphone x + 中本句才会生效 */
    padding-left: calc(env(safe-area-inset-left) + 0.5rem);
    padding-right: calc(env(safe-area-inset-right) + 0.5rem);


    .back-btn {
      //position: absolute;
      // margin-top: 1rem;
      // margin-top: env(safe-area-inset-top);
      // margin-top: calc(var(--ion-safe-area-top) + 0.5rem);
      top: 0.5rem;
      top: calc(var(--ion-safe-area-top) + env(safe-area-inset-top) + 0.5rem);
      position: absolute;
      z-index: 1;
      font-size: 0.7rem;
      height: 1.5rem;
      ion-button {
        font-size: 0.7rem;
        height: 1.5rem;
      }
    }
  }
}




.content {
  padding: 0.5rem;
  background: var(--content-page-background);
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  .sys-info {
    text-align: center;
    position: relative;
    width: 100%;
    height: 30%;

    .sys-logo {
      height: 70%;
      width: auto;
    }

    .sys-name {

    }
  }
  .login {
    height: 60%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
  }

  .app-footer {
    //position: absolute;
    //bottom: 0.5rem;
    //text-align: center;
    //width: 100%;
    //font-size: 0.6rem;
    // flex: 0.2;
    padding: 10px 0;
    color: #212121;
    font-size: 0.5rem;
    opacity: 0.45;

  }

}
