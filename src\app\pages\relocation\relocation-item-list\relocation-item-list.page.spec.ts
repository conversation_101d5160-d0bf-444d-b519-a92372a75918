import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RelocationItemListPage } from './relocation-item-list.page';

describe('RelocationItemListPage', () => {
  let component: RelocationItemListPage;
  let fixture: ComponentFixture<RelocationItemListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RelocationItemListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RelocationItemListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
