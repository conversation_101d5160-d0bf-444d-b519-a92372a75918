import { Injectable } from '@angular/core';
import {SqlService} from '@services/sql/sql.service'
import { dateFormat} from '../../../utils'

@Injectable({
  providedIn: 'root'
})
export class NoteListService {

  constructor(
    private sql: SqlService,
  ) {
    this.sql.init()
  }
  get isInit() {
    return this.sql.isInit
  }

  getCount() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT count(id) as count FROM tbl_note_list', [],
          (x, rs) => {
            console.log(rs)
            const res = rs.rows.item(0)
            resolve(res.count)
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  all() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_note_list order by id desc', [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              console.log(item.id, item.asset_check_no, item.asset_check_name, item.locationname, item.is_submit, item.list)
              arr.push({
                id: item.id,
                asset_check_no: item.asset_check_no,
                asset_check_name: item.asset_check_name,
                locationname: item.locationname,
                is_submit: item.is_submit,
                list: JSON.parse(item.list),
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  add(asset_check_no, asset_check_name, locationname, is_submit, list) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'INSERT INTO tbl_note_list (asset_check_no, asset_check_name, locationname, is_submit, list) VALUES (?,?,?,?,?)',
          [asset_check_no, asset_check_name, locationname, is_submit, JSON.stringify(list)],
          (x, rs) => {
            this.all().then(arr => {
              resolve(arr)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }

  get(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_note_list WHERE id=(?)', [id],
          (x, rs) => {
            if (rs && rs.rows && rs.rows.length > 0 ) {
              const item = rs.rows.item(0)
              console.log('sql row:', item)

              const data = {
                id: item.id, // 本地ID

                asset_check_no: item.asset_check_no,
                asset_check_name: item.asset_check_name,
                locationname: item.locationname,
                is_submit: item.is_submit,
                list: JSON.parse(item.list),
              }
              resolve(data)
            } else {
              reject(rs.rows)
            }
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  getByNo(asset_check_no) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_note_list WHERE asset_check_no=(?)', [asset_check_no],
          (x, rs) => {
            if (rs && rs.rows && rs.rows.length > 0 ) {
              const item = rs.rows.item(0)
              console.log('sql row:', item)

              const data = {
                id: item.id, // 本地ID

                asset_check_no: item.asset_check_no,
                asset_check_name: item.asset_check_name,
                locationname: item.locationname,
                is_submit: item.is_submit,
                list: JSON.parse(item.list),
              }
              resolve(data)
            } else {
              reject(rs.rows)
            }
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }

  delete(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_note_list WHERE id=(?)',
          [id],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  deleteByNo(asset_check_no) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_note_list WHERE asset_check_no=(?)',
          [asset_check_no],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  update({ id, asset_check_no, asset_check_name, locationname, is_submit, list }) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'UPDATE tbl_note_list SET asset_check_no=(?),asset_check_name=(?),locationname=(?),is_submit=(?),list=(?) WHERE id=(?)',

          [asset_check_no, asset_check_name, locationname, is_submit, JSON.stringify(list), id],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  clear() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_note_list',
          [],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
}
