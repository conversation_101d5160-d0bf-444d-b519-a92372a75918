import { Injectable } from '@angular/core';
import {Storage} from '@ionic/storage-angular';

@Injectable({
  providedIn: 'root'
})
export class StorageNativeService {
  private _storage: Storage | null = null;

  // private storage: Storage
  constructor(private storage: Storage) {
    this.init();
  }
  async init() {
    console.log('StorageNativeService init');
    this._storage = await this.storage.create();
  }


  /**
   * set.
   * @param key key
   * @param value value
   * Store a key value pair.
   */
  public async setItem(key: string, value: any) {
    console.log('StorageNativeService setItem', key, value);
    return await this._storage?.set(key, value);
  }


  /**
   * get
   * @param key key
   * @returns value value
   */
  public async getItem(key: string): Promise<any> | undefined {
    console.log('StorageNativeService getItem', key);
    return await this._storage?.get(key);
  }


  /**
   *
   * @param key
   * Remove item from storage based on key (name)
   */
  public async remove(key: string) {
    console.log('StorageNativeService remove', key);
    return await this._storage?.remove(key);
  }

  /**
   * Clear everything from storage
   */
  public async clear() {
    console.log('StorageNativeService clear');
    return await this._storage?.clear();
  }
}
