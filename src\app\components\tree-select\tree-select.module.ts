import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
// import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

// import { TreeSelectPage } from './tree-select.page';

// const routes: Routes = [
//   {
//     path: '',
//     component: TreeSelectPage
//   }
// ];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
  ],
  // declarations: [TreeSelectPage],
  // entryComponents: [TreeSelectPage],
  // exports: [TreeSelectPage],
})
export class TreeSelectPageModule {}
