<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onNoteList()">Back</ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onSave()">Save</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="full">
    <ion-list-header lines="full">
      <ion-label>W/O Article Information</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">Bar Code No.</ion-label>
      <ion-text class="item-value">{{ data.property_barcode }}</ion-text>
    </ion-item>
    <ion-item lines="full" detail (click)="searchItem()">
      <ion-label position="fixed">Name</ion-label>
      <ion-text class="item-value">{{ data.property_name }}</ion-text>
    </ion-item>
    <!--<ion-item lines="full">-->
    <!--  <ion-label position="fixed">Location</ion-label>-->
    <!--  <ion-text class="item-value">{{ data.location_name || getLocationName(data.property_location) }}</ion-text>-->
    <!--</ion-item>-->
    <ion-item-divider></ion-item-divider>
    <ion-list-header lines="full">
      <ion-label>W/O Apply</ion-label>
    </ion-list-header>
    <ion-item lines="full" detail (click)="onSelectLocation()">
      <ion-label position="fixed">
        <ion-text *ngIf="data.property_status === 1 || (data.location_list && data.location_list.length > 1)" color="danger">*</ion-text>
        Location</ion-label>
      <ion-text *ngIf="data.property_location" class="item-value">{{ getLocationName(data.property_location) }}</ion-text>
      <ion-text *ngIf="!data.property_location" class="item-value placeholder">Please Choose</ion-text>
    </ion-item>
    <ion-item [button]="false" [detail]="false" lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>W/O Reason</ion-label>
      <div style="width: 100%">
        <ion-select [(ngModel)]="data.property_status_change_woreason" class="item-value" placeholder="Please Choose" okText="Confirm" cancelText="Cancel" style="width: 100%; max-width: 100%;">
          <ion-select-option value="0">Damaged</ion-select-option>
          <ion-select-option value="1">Lost</ion-select-option>
          <ion-select-option value="2">Stolen</ion-select-option>
          <ion-select-option value="3">Donate</ion-select-option>
          <ion-select-option value="4">Sold</ion-select-option>
          <ion-select-option value="5">Replace</ion-select-option>
          <ion-select-option value="6">Disrepair</ion-select-option>
          <ion-select-option value="7">Expiried</ion-select-option>
          <ion-select-option value="8">Return</ion-select-option>
          <ion-select-option value="10">Consumed</ion-select-option>
          <ion-select-option value="9">Other</ion-select-option>
        </ion-select>
      </div>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>W/O Qty.</ion-label>
      <ion-input [(ngModel)]="data.change_num" type="number" placeholder="Please Fill Qty."></ion-input>
    <!-- class="ion-text-right"    (ionFocus)="onFocus($event)" -->
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>W/O Photo</ion-label>
      <div class="img-box" tappable>
        <div class="img-wrapper">
          <img *ngIf="data.img" [src]="data.img" alt="" (click)="imgViewer()">
          <div *ngIf="!data.img" class="add-img" (click)="addImg()">
            <ion-icon icon="add" ></ion-icon>
            <!--<ion-icon [icon]="data.img ? 'remove-circle-outline' : 'add'" ></ion-icon>-->
          </div>
          <div *ngIf="data.img" class="remove-img" (click)="removeImg()"><ion-icon icon="close-circle-outline"></ion-icon></div>
        </div>
      </div>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Remark</ion-label>
      <!--<ion-text>{{ data.property_cancellation_remark }}</ion-text>-->
      <ion-textarea [(ngModel)]="data.property_status_change_remark" class="remark-input" placeholder="Remark"></ion-textarea>
    </ion-item>
  </ion-list>
</ion-content>
