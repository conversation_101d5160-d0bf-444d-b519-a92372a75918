//page-scan {
//  .scroll-content {
//    //background: transparent none;
//  }
//  .qrscanner {
//    background: none;
//    &-area {
//      width: 100%;
//      height: 86%;
//      //background: url(../../../assets/imgs/scanner.svg) no-repeat center center;
//      background-size: contain;
//    }
//  }
.tips {
  left: 0;
  width: 100%;
  position: absolute;
  top: 22%;
  font-size: 13px;
  color: #FFFFFF;
  text-align: center;
  z-index: 5;
}

.through-line {
  left: 25%;
  width: 52%;
  height: 2px;
  background: red;
  position: absolute;
  animation: myfirst 1s linear infinite alternate;
}

//
@keyframes myfirst {
  0% {
    background: red;
    top: 40%;
  }
  25% {
    background: yellow;
    top: 45%;
  }
  50% {
    background: blue;
    top: 50%;
  }
  75% {
    background: green;
    top: 55%;
  }
  100% {
    background: red;
    top: 60%;
  }
}

//  .button-bottom {
//    width: 128px;
//    position: absolute;
//    left: 50%;
//    bottom: 80px;
//    margin-left: -64px;
//    .icon-camera {
//      float: left;
//    }
//  }
//}


page-home {

}

// .content {
//     background: none transparent;
// }

// .button {
//     background: none;
// }

// html , body, #my_page {
//     background: none;
// }

.scan-area {
  display: block;
  width: 100%;
  height: 100vh;
  margin-left: auto;
  margin-right: auto;
  position: absolute;
  top: 0;
  left: 0;
}

.scan-box {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  border: 100vw solid rgba(0, 0, 0, 0.51);
  //height: 250px;
  //width: 250px;
  //padding: 100px;
  height: 50vw;
  width: 50vw;
  padding: 35vw 35vw;
  background: transparent;


}

.scanner-content {
  display: none;

  &.show {
    display: block;
  }
}

.content {
  opacity: 0;
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
  -webkit-animation-duration: .6s;
  animation-duration: .6s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-delay: .6s; /**延迟动画**/
  animation-delay: .6s;

}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0.3
  }

  100% {
    opacity: 1
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0.3
  }

  100% {
    opacity: 1
  }
}


.confirm-area {
  background: white;
  position: absolute;
  top: 75%;
  width: 50%;
  margin: 0 25%;
  text-align: center;
  border-radius: 15px;
  z-index: 3;

  line-height: 0.4rem;
  .confirm-label {
    line-height: 35px;
    vertical-align: middle;
    font-size: 13px;
  }
  .confirm-value {
    vertical-align: middle;
  }
}
.type-select {

  /* background: white; */
  //position: absolute;
  top: 5%;
  left: 5%;
  /* margin: 0 25%; */
  z-index: 10;
  font-size: 0.7rem;
  width: 50%;
  border-radius: 0.3rem;
  color: #FFFFFF;
  --color: #FFFFFF;
  //border: 1px solid #FFFFFF;
  padding: 0;



}

.back-btn {

  z-index: 44;
  position: absolute;
  right: 5%;
  top: 5%;
  padding: 0;
  margin: 0;
  ion-button {
    //min-height: 48px;
    //margin: 2px;
    min-height: 1rem;
    margin: 1px;
    height: 1.8rem;
  }
}

//@media (max-width: 479px)
//  /* uncomment for only portrait: */
//  /* and (orientation: portrait) */
//  /* uncomment for only landscape: */
//  /* and (orientation: landscape) */ {
//
//  .type-select {
//    min-height: 1.5rem;
//    height: 2rem;
//
//    ion-segment-button {
//      max-width: 23%;
//      font-size: 0.7rem;
//      padding: 0;
//      margin: 0;
//      min-height: 1rem;
//      ion-label {
//        max-width: 100%;
//        font-size: 0.5rem;
//      }
//
//      &.segment-button-checked {
//        border-radius: 0.2rem;
//      }
//    }
//  }
//
//}
div.ac {
  display: block;
  width: 50%;
  position: absolute;
  top: 5%;
  left: 5%;
  z-index: 10;
  overflow: hidden;
  border-radius: 0.3rem;
  border: 1px solid #FFFFFF;

  .type-select{
    /* position: absolute; */
    /* top: 5%; */
    /* left: 5%; */
    /* z-index: 10; */
    font-size: 0.7rem;
    width: 100%;
    /* border-radius: 0.3rem; */
    color: #FFFFFF;
    --color: #FFFFFF;
    /* border: 1px solid #FFFFFF; */
    padding: 0;
    height: 1.8rem;


    ion-segment-button {

      min-width: 30px;
      min-height: 20px;
      width: 50%;
      --padding-start: 0;
      --padding-end: 0;
      --margin-start: 0;
      --margin-end: 0;
      position: relative;
      font-size: 0.5rem;


    }
  }
}
