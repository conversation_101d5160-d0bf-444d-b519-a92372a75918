{"name": "asset-ionic", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "engines": {}, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "14.1.3", "@angular/common": "14.1.3", "@angular/compiler": "14.1.3", "@angular/core": "14.1.3", "@angular/fire": "^7.4.1", "@angular/forms": "14.1.3", "@angular/localize": "14.1.3", "@angular/platform-browser": "14.1.3", "@angular/platform-browser-dynamic": "14.1.3", "@angular/router": "14.1.3", "@awesome-cordova-plugins/android-notch": "^6.8.0", "@awesome-cordova-plugins/camera": "^6.8.0", "@awesome-cordova-plugins/core": "^6.3.0", "@awesome-cordova-plugins/native-storage": "^6.3.0", "@awesome-cordova-plugins/sqlite": "6.3.0", "@ionic-native/app-version": "^5.36.0", "@ionic-native/brightness": "^5.33.1", "@ionic-native/camera": "^5.36.0", "@ionic-native/core": "^5.36.0", "@ionic-native/device": "^5.36.0", "@ionic-native/file": "^5.36.0", "@ionic-native/http": "^5.36.0", "@ionic-native/is-debug": "^5.36.0", "@ionic-native/native-audio": "^5.26.0", "@ionic-native/native-storage": "5.36.0", "@ionic-native/network": "^5.36.0", "@ionic-native/photo-viewer": "^5.36.0", "@ionic-native/qr-scanner": "^5.36.0", "@ionic-native/secure-storage": "^5.36.0", "@ionic-native/splash-screen": "^5.36.0", "@ionic-native/status-bar": "^5.36.0", "@ionic-native/themeable-browser": "^5.23.0", "@ionic-native/unique-device-id": "^5.23.0", "@ionic-native/vibration": "^5.36.0", "@ionic/angular": "^6.2.2", "@ionic/cordova-builders": "^7.0.0", "@ionic/storage-angular": "^4.0.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "axios": "^0.19.0", "chroma-js": "^2.0.2", "com-sarriaroman-photoviewer": "^1.2.5", "cordova-android": "^13.0.0", "cordova-plugin-advanced-http": "^3.3.1", "cordova-plugin-app-version": "^0.1.14", "cordova-plugin-camera": "^7.0.0", "cordova-plugin-file": "^7.0.0", "cordova-plugin-file-opener2": "^3.0.5", "cordova-plugin-get-app-version": "^1.0.1", "cordova-plugin-is-debug": "^1.0.0", "cordova-plugin-nativeaudio": "^3.0.9", "cordova-plugin-network-information": "^3.0.0", "cordova-plugin-qrscanner": "^3.0.1", "cordova-plugin-secure-storage": "^3.0.2", "cordova-plugin-uniquedeviceid": "^1.3.2", "cordova-plugin-vibration": "^3.1.1", "cordova-plugin-whitelist": "^1.3.5", "core-js": "^3.24.1", "eventemitter3": "^4.0.7", "hammerjs": "^2.0.8", "ionicons": "^6.0.3", "js-cookie": "^2.2.1", "md5": "^2.3.0", "node-polyfill-webpack-plugin": "^2.0.1", "rxjs": "^7.5.6", "sharp": "0.30.7", "tslib": "^2.4.0", "zone.js": "~0.11.8"}, "devDependencies": {"@angular-devkit/architect": "~0.1401.2", "@angular-devkit/build-angular": "14.1.3", "@angular-devkit/core": "14.1.3", "@angular-devkit/schematics": "14.1.3", "@angular/cli": "14.1.3", "@angular/compiler": "14.1.3", "@angular/compiler-cli": "14.1.3", "@angular/language-service": "14.1.3", "@ionic/angular-toolkit": "~7.0.0", "@types/jasmine": "~4.0.3", "@types/jasminewd2": "^2.0.10", "@types/node": "~18.7.2", "codelyzer": "^6.0.2", "cordova-plugin-android-notch": "^1.0.3", "cordova-plugin-androidx": "^3.0.0", "cordova-plugin-androidx-adapter": "^1.1.3", "cordova-plugin-brightness": "^0.2.0", "cordova-plugin-device": "^2.1.0", "cordova-plugin-ionic-keyboard": "^2.2.0", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-splashscreen": "^5.0.3", "cordova-plugin-statusbar": "^3.0.0", "cordova-sqlite-storage": "^6.1.0", "jasmine-core": "~4.3.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.1", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "ts-node": "~10.9.1", "tslint": "~5.20.1", "typescript": "~4.7.4"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-statusbar": {}, "cordova-plugin-device": {}, "cordova-plugin-ionic-webview": {"ANDROID_SUPPORT_ANNOTATIONS_VERSION": "27.+"}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-advanced-http": {"OKHTTP_VERSION": "3.10.0", "ANDROIDBLACKLISTSECURESOCKETPROTOCOLS": "SSLv3,TLSv1"}, "cordova-plugin-secure-storage": {}, "cordova-plugin-is-debug": {}, "cordova-plugin-vibration": {}, "cordova-sqlite-storage": {}, "cordova-plugin-network-information": {}, "cordova-plugin-file": {}, "cordova-plugin-file-opener2": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}, "cordova-plugin-camera": {"ANDROIDX_CORE_VERSION": "1.6.+"}, "cordova-plugin-android-notch": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-androidx-adapter": {}, "cordova-plugin-nativeaudio": {}, "cordova-plugin-uniquedeviceid": {}, "cordova-plugin-brightness": {}, "cordova-plugin-qrscanner": {}, "cordova-plugin-get-app-version": {}, "cordova-plugin-app-version": {}}, "platforms": ["ios", "android"]}, "config": {"ionic_webpack": "./webpack.config.js"}}