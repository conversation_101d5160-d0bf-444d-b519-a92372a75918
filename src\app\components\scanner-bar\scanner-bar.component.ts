import {Component, Input, Output, EventEmitter, OnInit} from '@angular/core'

@Component({
  selector: 'app-scanner-bar',
  templateUrl: './scanner-bar.component.html',
  styleUrls: ['./scanner-bar.component.scss'],
})
export class ScannerBarComponent implements OnInit {
  @Input() public label: string
  @Input() public placeholder: string
  @Input() public bg: string
  @Input() public type: string
  @Input() public value: string
  @Input() public showScanner = false
  @Input() public scannerPosition = 'b'
  @Output() public valueChange = new EventEmitter();
  @Output() public scan = new EventEmitter();
  @Output() public submit = new EventEmitter();
  @Output() public blur = new EventEmitter();

  public t = 0

  constructor() {
  }

  ngOnInit() {
    // console.log(`--background: ${this.bg}!important;`)
    this.t = new Date().getTime()
  }
  get currentClass() {
    switch (this.type) {
      case 'book':
        return 'book-bar'
      case 'reader':
        return 'reader-bar'
      case 'preBorrow':
      case 'racking':
        return 'dark-green-bar'
      case 'return-book':
        return 'light-green'
      default:
        return ''
    }
  }
  get currentStyle() {
    if (this.bg) {

      return `--background: ${this.bg}!important;`
    } else {
      return ''
    }
  }
  get className() {
    return 'scanner-bar ' + (this.scannerPosition === 'r' ? 'right' : 'bottom')
  }

  get showRightScanner() {
    return this.scannerPosition === 'r' && this.showScanner
  }
  get showBottomScanner() {
    return this.scannerPosition !== 'r' && this.showScanner
  }

  handleChange() {
    // console.log(this.value)
    this.valueChange.emit(this.value)
  }
  handleBlur() {
    this.blur.emit()
  }
  handleSubmit() {
    const now = new Date().getTime()
    if ((now - this.t) < 200) {
      return
    }
    this.t = now
    this.handleChange()
    this.submit.emit()
  }

  handleScan() {
    this.scan.emit()
  }
}
