import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { CardModule } from '@app/components/card/card.module'
import { BorrowSearchPage } from './borrow-search.page';

const routes: Routes = [
  {
    path: '',
    component: BorrowSearchPage
  }
];
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    CardModule,
  ],
  declarations: [BorrowSearchPage],
  // entryComponents: []
})
export class BorrowSearchPageModule {}
