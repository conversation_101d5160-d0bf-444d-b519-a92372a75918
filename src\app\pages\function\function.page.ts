import { Component, OnInit } from '@angular/core';
import {NavController, MenuController, ModalController, Platform} from '@ionic/angular'
// import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ScannerPagePage} from '@pages/scanner-page/scanner-page.page'
import {TranslateService} from '@ngx-translate/core'
import {ScannerService} from '@app/utils/scanner.service'
import {UtilsService} from '@app/utils/utils.service'
import {LoadingService} from '@app/utils/loading.service'
import {Router, ActivatedRoute} from '@angular/router'
import {PermissionService} from '@services/permissions/permission.service'
import {AssetService} from '@services/api/asset.service'
import { SqlService } from '@services/sql/sql.service'
import { PastRecordService } from '@services/sql/past-record.service'
import {RelocationService} from '@services/sql/relocation.service'

@Component({
  selector: 'app-function',
  templateUrl: './function.page.html',
  styleUrls: ['./function.page.scss'],
})
export class FunctionPage implements OnInit {
  public barCodeNo = ''
  public records: any = []
  constructor(
    private platform: Platform,
    private navController: NavController,
    private menu: MenuController,
    private pastRecord: PastRecordService,
    private modalController: ModalController,
    public translate: TranslateService,
    private loading: LoadingService,
    private scanner: ScannerService,
    public utils: UtilsService,
    private sql: SqlService,
    public asset: AssetService,
    private router: Router,
    private navCtrl: NavController,
    public activeRouter: ActivatedRoute,
    public relocationService: RelocationService,
    public permissionService: PermissionService,
    ) { }

  ngOnInit() {
    // this.sql.init()
    // this.sql.clear()
    this.platform.ready().then(() => {
      this.permissionService.init()
      this.loadPastRecords()
    })
  }
  get onLine() {
    return navigator.onLine
  }
  get hasModule() {
    return this.permissionService.hasModule('AssetCheck')
  }
  showModule(code) {
    return this.permissionService.hasPermission_View(code)
  }
  loadPastRecords() {
    return new Promise((resolve, reject) => {
      if (this.pastRecord.isInit) {
        this.pastRecord.all().then(records => {
          this.records = records
          console.log('記錄加載成功', records)
          resolve(records)
        }).catch(err => {
          reject(err)
        })
      } else {
        reject('no init')
      }
    })
  }

  onBack() {
    this.navCtrl.pop()
    // this.navController.navigateRoot('/login')
  }
  onLogout() {
    this.navCtrl.navigateBack('login')
  }
  onSearchBarCodeNo() {
    this.searchItem(this.barCodeNo)
  }
  async onScanBarCodeNo() {

    try {
      const text = await this.scanner.showScanModal({
        title: this.translate.instant('Scan QR Code'),
        tips: this.translate.instant('Please place the bar code in the box below'),
        page: this,
      })
      console.log(text)
      if (text) {
        await this.searchItem(text)
      }
      // await this.loadData(text)
    } catch (e) {
      this.utils.showToast(e)
    }
  }
  async onItemRecord(item) {
    await this.searchItem(item.code)
  }
  async searchItem(codeno) {
    if (!codeno) { return }
    await this.loading.start()
    let data = null
    let itemurl = ''
    try {
      data = await this.asset.getItemUrl(codeno)
      itemurl = data.itemurl
      console.log(data)
    } catch (err) {
      console.error(err)
      this.utils.showMsg('未找到指定物品！')
      await this.loading.end()
      return
    }
    try {
      await this.pastRecord.add(codeno, data.propertyname)
    } catch (e) {
      console.error(e)
    }
    if (itemurl) {
      this.onCloseMenu()
      this.openItemDetail(codeno, itemurl)

    }

    await this.loading.end()
  }

  openItemDetail(codeno, itemurl) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['/item-detail/item'],
      {
        queryParams: {
          codeno,
          url: itemurl
        },
      }
    )
  }

  onRecordMenu() {
    this.loadPastRecords().then(() => {
      this.menu.open('past-record')
    })
  }
  onCloseMenu() {
    this.menu.close()
  }


  async showScanModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ScannerPagePage,
      componentProps: {
        ...props,
        id: 'scanner-modal'
      },
      id: 'scanner-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    return res.data && res.data.text || ''
  }


  /*       盤點            */
  in() {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['inventory-list'],
    )
  }
  /*       註銷            */
  cancellation() {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['cancellation-list'],
    )
  }
  /*       轉移            */
  async relocation() {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['relocation-list'],
    )
  }
  /*       部門調撥            */
  async allocation() {
    if (!this.onLine) {
      this.utils.showMsg('Please check WIFI connection')
      return
    }
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['allocation-search'],
    )
  }
  /*       物品借領            */
  async borrow() {
    if (!this.onLine) {
      this.utils.showMsg('Please check WIFI connection')
      return
    }
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['borrow-search'],
    )
  }
  /*       物品借領            */
  async return() {
    if (!this.onLine) {
      this.utils.showMsg('Please check WIFI connection')
      return
    }
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['return-search'],
    )
  }
}
