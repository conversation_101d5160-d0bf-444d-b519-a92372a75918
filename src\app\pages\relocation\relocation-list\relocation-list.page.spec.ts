import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RelocationListPage } from './relocation-list.page';

describe('RelocationListPage', () => {
  let component: RelocationListPage;
  let fixture: ComponentFixture<RelocationListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RelocationListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RelocationListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
