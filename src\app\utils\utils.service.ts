import {Injectable} from '@angular/core'
import {Alert<PERSON>ontroller, ToastController, ModalController} from '@ionic/angular'
import {StorageService} from '../services/storage/storage.service'
import {TranslateService} from '@ngx-translate/core'

@Injectable({
  providedIn: 'root'
})
export class UtilsService {
  private duration = 2000
  private position = 'bottom'

  constructor(
    private toastCtrl: ToastController,
    private alertController: AlertController,
    private storageService: StorageService,
    public translate: TranslateService,
    private modalCtrl: ModalController,
  ) {
  }


  async showToast(msg: string, duration?: number, position?: 'bottom" | "top" | "middle') {
    if (!msg) {
      return
    }
    const toast = await this.toastCtrl.create({
      message: msg,
      duration: duration === undefined ? this.duration : duration,
      // @ts-ignore
      position: position === undefined ? this.position : position,
      translucent: true,
      showCloseButton: false,
      cssClass: 'utils-toast',
      closeButtonText: 'Close',
    })
    toast.present()
  }
  async showMsg(msg: string, title?: string, btn?: string) {
    if (!msg) {
      return
    }
    const header = (title === null || title === undefined) ? this.translate.instant('MESSAGE.TIPS') : title
    const btnText = (btn === null || btn === undefined) ?  this.translate.instant('BUTTON.CONFIRM') : btn
    return new Promise(async (resolve) => {
      const alertObj = await this.alertController.create({
        header,
        message: msg,
        buttons: [{
          text: btnText,
          handler: () => {
            return resolve(true);
          },
        }],
      })
      await alertObj.present()
    });
  }

  // 轉換 realHost 格式
  convertRealHost(hostStr: string) {
    if (!hostStr) { return ''; }

    // 處理不同格式的 URL
    if (hostStr.startsWith('http://') || hostStr.startsWith('https://')) {
      // 已經包含協議的完整 URL
      return hostStr;
    } else if (hostStr.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/)) {
      // IP 地址格式，預設使用 http 協議
      return `http://${hostStr}`;
    } else {
      // 純域名，預設使用 https 協議
      return `https://${hostStr}`;
    }
  }

  public getImgUrl(img, isError) {
    if (img && !isError) {
      const {realHost, remoteProjectName, uri} = this.storageService.serverSetting
      const path = `${realHost}/${remoteProjectName}/${uri}/`

      return path + img
    } else {
      return 'assets/images/nopic.jpg'
    }
  }
  public get errorImage() {
    return 'assets/images/nopic.jpg'
  }
  public async showModal(component, componentProps) {
    let id = 'modal-' + new Date().getTime()
    if (!componentProps.domId) {
      componentProps.domId = id
    } else {
      id = componentProps.domId
    }
    const modal = await this.modalCtrl.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component,
      componentProps,
      id
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    return res
  }

  public async confirm(title, msg, confirmText?, cancelText?) {
    let confirm = false
    const alert = await this.alertController.create({
      header: title,
      message: msg,
      backdropDismiss: false,
      buttons: [
        {
          text: cancelText || this.translate.instant('BUTTON.CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            console.log('CANCEL');
          }
        }, {
          text: confirmText || this.translate.instant('BUTTON.CONFIRM'),
          handler: () => {
            confirm = true
            console.log('CONFIRM');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss();
    return confirm
  }
}
