import { Injectable } from '@angular/core';
import { SQLite, SQLiteObject } from '@awesome-cordova-plugins/sqlite/ngx';
@Injectable({
  providedIn: 'root'
})
export class SqlService {

  public db: SQLiteObject
  public isInit = false
  constructor(private sqlite: SQLite) { }


  init() {
    // this.clear()
    this.sqlite.create({
      name: 'asset.db',
      location: 'default',
      key: '2020-aseet.norrayhk.com',
      createFromLocation: 1
    }).then((db: SQLiteObject) => {
      this.db = db
      this.isInit = true
      // 查詢記錄
      db.transaction(tx => {
        // tx.executeSql('DROP TABLE tbl_cancellation')
        tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_past_records (id INTEGER PRIMARY KEY AUTOINCREMENT, code, item_name, search_time )')
        // tslint:disable-next-line:max-line-length
        tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_note_list (id INTEGER PRIMARY KEY AUTOINCREMENT, asset_check_no, asset_check_name, locationname, is_submit, list )')
        // 物品位置轉移
        // tslint:disable-next-line:max-line-length
        tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_relocation (id INTEGER PRIMARY KEY AUTOINCREMENT, ' +
          'property_barcode, property_name, property_no, department_name, department_ename, kind_name, kind_ename, ' +
          'serial_no, property_maint_fmdate, property_maint_todate, supplier_name, property_status,' +
          ' location_name, location_ename, property_location_serial_no, property_location_serial_ctime,' +
          ' property_location, change_num, relocation_location_no, property_relocation_remark' +
          ' )')
        // 物品報銷
        tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_cancellation (id INTEGER PRIMARY KEY AUTOINCREMENT, ' +
          'property_location_serial_no, property_barcode, property_location,' +
          'change_num, img, property_status_change_woreason, property_status_change_remark,' +
          'location_list,' +
          'property_no, property_ename, property_name, property_status' +
          ' )')
        // tslint:disable-next-line:max-line-length
        tx.executeSql('CREATE TABLE IF NOT EXISTS tbl_all_location (id INTEGER PRIMARY KEY AUTOINCREMENT, no, location_name, location_ename, pid, isParent, child )')
        // tx.executeSql('SELECT * FROM tbl_past_records', [],
        //   (x, rs) => {
        //     for (let i = 0; i < rs.rows.length; i++) {
        //       const item = rs.rows.item(i)
        //       console.log(item.id, item.code, item.item_name, item.search_time)
        //     }
        //   }, (x, error) => {
        //   console.log('SELECT error: ' + error.message);
        // });
      })
    }).catch(err => {
      console.error(err)
    })
  }
  clear() {
    this.sqlite.deleteDatabase({
      name: 'asset.db',
      location: 'default',
      key: '2020-aseet.norrayhk.com',
      createFromLocation: 1
    })
  }
}
