import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ReturnItemListPage } from './return-item-list.page';

describe('ReturnItemListPage', () => {
  let component: ReturnItemListPage;
  let fixture: ComponentFixture<ReturnItemListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ReturnItemListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ReturnItemListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
