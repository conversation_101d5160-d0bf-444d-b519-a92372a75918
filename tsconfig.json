{"compileOnSave": false, "compilerOptions": {"baseUrl": "./src", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "esnext", "moduleResolution": "node", "emitDecoratorMetadata": false, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"@app/*": ["app/*"], "@assets/*": ["assets/*"], "@pages/*": ["app/pages/*"], "@services/*": ["app/services/*"]}}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true, "emitDecoratorMetadata": false}}