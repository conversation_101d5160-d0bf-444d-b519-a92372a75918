import {ApplicationRef, Component, Input, OnInit} from '@angular/core'
import {ModalController, NavParams} from '@ionic/angular'
import {AssetService} from '@services/api/asset.service'
import {UtilsService} from '@app/utils/utils.service'
import {LoadingService} from '@app/utils/loading.service'
import {RelocationService} from '@services/sql/relocation.service'

@Component({
  selector: 'app-tree-select',
  templateUrl: './tree-select.page.html',
  styleUrls: ['./tree-select.page.scss'],
})
export class TreeSelectPage implements OnInit {
  @Input() parentId = '0'
  public title = 'Select'
  public list: any = []
  public currentList: any = []
  public paths: any = []
  public isChild: any = false
  public current: any = ''
  public codeKey: any = ''
  public nameKey: any = ''
  constructor(
    private modal: ModalController,
    private asset: AssetService,
    private utils: UtilsService,
    private loading: LoadingService,
    private relocationService: RelocationService,
    private app: ApplicationRef,
    private params: NavParams,
  ) {
    console.log('list', params.get('list'));
    this.codeKey = params.get('codeKey')
    this.nameKey = params.get('nameKey')
    const list = params.get('list')
    this.title = params.get('title') || 'Select'
    this.init(list)
  }

  ngOnInit() {
    // this.init()
  }
  onCancel() {
    this.modal.dismiss({
      cancel: true
    })
  }
  onSave() {
    const item = this.currentList.find(i => i.check)
    if (item) {
      this.modal.dismiss({
        save: true,
        data: item
      })
    } else {
      console.log('未選擇')
    }
  }

  checkInit(list) {
    list.forEach(item => {
      item.check = false
      if (item && item.child) {
        this.checkInit(item.child)
      }
    })
  }

  async init(res) {
    await this.loading.start()
    try {
      // const res = await this.relocationService.getLocation()
      console.log(res)
      this.checkInit(res)
      this.list = res
      this.currentList = res
      this.paths = [res]
      this.app.tick();
    } catch (e) {
      console.error(e)
    }
    await this.loading.end()
  }

  onClickItem(item) {
    if (item.isParent === 'true') {
      this.onChangeItem({})
      this.isChild = true
      this.currentList = item.child
      this.paths.push(item.child)
      this.app.tick();
    } else {
      console.log('選擇地址：', item)
      // this.modal.dismiss({
      //   save: true,
      //   data: item
      // })
      item.check = !item.check
      this.onChangeItem(item)
    }
  }
  async selectChild(item) {
    // const res: any = await this.utils.showModal(TreeSelectPage, {parentId: id})
    // if (res.data.save) {
    //   this.modal.dismiss({
    //     save: true,
    //     data: res.data.data
    //   })
    // }
    this.app.tick();
  }
  onBackParent() {
    this.onChangeItem({})
    this.paths.pop()
    this.currentList = this.paths[this.paths.length - 1]
    this.isChild = this.paths.length > 1
  }
  onChangeItem(item) {
    console.log(item)
    this.currentList.forEach(i => {
      if (i[this.codeKey] !== item[this.codeKey]) {
        i.check = false
      }
    })
  }
}
