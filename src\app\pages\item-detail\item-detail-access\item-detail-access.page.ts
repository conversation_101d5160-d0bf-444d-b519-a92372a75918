import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router, NavigationEnd} from '@angular/router'
import {Platform, NavController} from '@ionic/angular'
import {ThemeableBrowser} from '@ionic-native/themeable-browser/ngx'
import {DomSanitizer} from '@angular/platform-browser'
import {Subscription} from 'rxjs'

@Component({
  selector: 'app-item-detail-access',
  templateUrl: './item-detail-access.page.html',
  styleUrls: ['./item-detail-access.page.scss'],
})
export class ItemDetailAccessPage implements OnInit {

  public url: any = ''
  private subscription: Subscription;
  constructor(
    private router: Router,
    // private browserTab: BrowserTab
    private routeInfo: ActivatedRoute,
    private platform: Platform,
    private themeableBrowser: ThemeableBrowser,
    private sanitizer: DomSanitizer,
    private nav: NavController,
  ) {
  }

  ngOnInit() {

    this.platform.ready().then(() => {
      // this.url = this.sanitizer.bypassSecurityTrustResourceUrl('https://www.baidu.com/');
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.url = this.routeInfo.snapshot.queryParams.url

      this.subscription = this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd ) {
          if (event instanceof NavigationEnd && event.url.indexOf('/item-detail/access') >= 0) {
            this.init();
          }
        }
      });
    })
  }
  onClose() {
    this.nav.pop();
    // this.nav.navigateBack('function')
  }

  init() {
    const url = this.routeInfo.snapshot.queryParams.url
    this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }


  ionViewDidEnter() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      const url = this.routeInfo.snapshot.queryParams.url
      this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);
    })
  }

}
