import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TreeSelectPage } from './tree-select.page';

describe('TreeSelectPage', () => {
  let component: TreeSelectPage;
  let fixture: ComponentFixture<TreeSelectPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TreeSelectPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TreeSelectPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
