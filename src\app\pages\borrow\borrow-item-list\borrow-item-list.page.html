<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onBackToHome()">Back</ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onSave()">Submit</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="full">
    <ion-list-header lines="full">
      <ion-label>Article Information</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">Barcode No.</ion-label>
      <ion-text class="item-value">{{ data.property_barcode }}</ion-text>
    </ion-item>
    <ion-item lines="full" detail (click)="searchItem()">
      <ion-label position="fixed">Article Name</ion-label>
      <ion-text class="item-value">{{ data.property_name }}</ion-text>
      <ion-text slot="end" class="item-value placeholder">Detail</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Department</ion-label>
      <ion-text class="item-value">{{ data.old_department_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Category</ion-label>
      <ion-text class="item-value">{{ data.old_kind_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Location</ion-label>
      <ion-text class="item-value">{{ data.location_name }}</ion-text>
    </ion-item>
    <ion-item-divider></ion-item-divider>
    <ion-list-header lines="full">
      <ion-label>Borrow Info.</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>Borrow To</ion-label>
      <div style="width: 100%">
        <ion-select
            [(ngModel)]="data.property_borrow_object"
            class="item-value"
            placeholder="Select"
            okText="Confirm"
            cancelText="Cancel" style="width: 100%; max-width: 100%;">
          <ion-select-option value="1">Staff</ion-select-option>
          <ion-select-option value="2">Other people</ion-select-option>
        </ion-select>
      </div>
    </ion-item>
    <ion-item *ngIf="data.property_borrow_object === '1'" lines="full" detail (click)="onSelectStaff()">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>Staff</ion-label>
      <ion-text *ngIf="data.staff_code" class="item-value">{{ data.staff_cname }}</ion-text>
      <ion-text *ngIf="!data.staff_code" class="item-value placeholder">Please Choose</ion-text>
    </ion-item>
    <ion-item *ngIf="data.property_borrow_object === '2'" lines="full">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>Other people</ion-label>
      <ion-input [(ngModel)]="data.property_borrow_to_name" type="text" placeholder="Enter"></ion-input>
    </ion-item>
    <ion-item lines="full" (click)="onClickExpectReturnDate()">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>Expect Return Date</ion-label>
    <!-- <ion-datetime
            #datetime
            [(ngModel)]="data.property_borrow_return_time"
            [min]="minDate"
            [max]="maxDate"
            displayFormat="YYYY-MM-DD"
            placeholder="Select Date"
            cancelText="Cancel"
            doneText="Confirm"></ion-datetime> -->
        <ion-datetime-button datetime="property_borrow_return_time" color="light" style="--ion-color-step-300: transparent;margin-left: -6px;">
          <span slot="date-target" [class]="propertyBorrowReturnTimeClassName">{{ propertyBorrowReturnTime }}</span>
        </ion-datetime-button>
        <ion-modal [isOpen]="isModalOpen" [keepContentsMounted]="true" (didDismiss)="onBlurExpectReturnDate()">
          <ng-template>
            <ion-datetime
              #actDatetime
              [(ngModel)]="data.property_borrow_return_time"
              [showDefaultButtons]="true"
              presentation="date"
              id="property_borrow_return_time"
              min="1990"
              max="2050"
              doneText="Confirm"
              cancelText="Cancel"
            ></ion-datetime>
          </ng-template>
        </ion-modal>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>Borrow Qty.</ion-label>
      <ion-input [(ngModel)]="data.change_num" type="number" [min]="0" placeholder="Please Fill Qty."></ion-input>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Remark</ion-label>
      <ion-textarea [(ngModel)]="data.property_borrow_remark" class="remark-input" placeholder="Remark"></ion-textarea>
    </ion-item>
  </ion-list>
</ion-content>
