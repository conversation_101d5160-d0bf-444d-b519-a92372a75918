import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ItemDetailAccessPage } from './item-detail-access.page';

const routes: Routes = [
  {
    path: '',
    component: ItemDetailAccessPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [ItemDetailAccessPage]
})
export class ItemDetailAccessPageModule {}
