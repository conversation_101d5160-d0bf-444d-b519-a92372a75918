import { Component, OnInit } from '@angular/core';
import {Platform} from '@ionic/angular'
import {Router} from '@angular/router'
import { SchoolService } from '@services/api/school.service'
import {StorageService} from '@services/storage/storage.service'
import {TranslateService} from '@ngx-translate/core'
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import { AppVersion } from '@ionic-native/app-version/ngx';

@Component({
  selector: 'app-index',
  templateUrl: './index.page.html',
  styleUrls: ['./index.page.scss'],
})
export class IndexPage implements OnInit {

  public cmpyUUID = ''
  public cmpyToken = ''
  public deviceID = ''
  public realHost = ''
  public project = ''
  public cmpyInit = false
  public settingInit = false

  public logoClassName = 'sch-logo'
  public logopath = ''
  public schcname = ''
  public schename = ''

  public version = ''
  public poweredBy = ''
  public hotline = ''

  constructor(
    private platform: Platform,
    private router: Router,
    private nativeStorage: StorageNativeService,
    private schoolService: SchoolService,
    private storage: StorageService,
    public translate: TranslateService,
    private appVersion: AppVersion
  ) {
    this.poweredBy = this.storage.appInfo.poweredBy
    this.hotline = this.storage.appInfo.hotline
  }

  ngOnInit() {
    this.platform.ready()
        .then(() => {
          this.appVersion.getVersionNumber().then((version) => {
            this.version = version
          })
        })
  }

  ionViewWillEnter() {
    this.platform.ready()
      .then(() => {
        this.init()
      })
  }

  get LogoImg() {
    if (this.settingInit && this.realHost && this.project && this.logopath) {
      return `${this.realHost}/${this.project}/Runtime/logo/${this.logopath}`
    }
    return ''
  }
  get canlogin() {
    return this.settingInit && this.realHost && this.project
  }

  async init() {
    try {
      this.cmpyUUID = await this.nativeStorage.getItem('cmpyUUID')
      this.cmpyToken = await this.nativeStorage.getItem('cmpyToken')
      this.cmpyInit = await this.nativeStorage.getItem('cmpyInit') === '1'
      this.realHost = await this.nativeStorage.getItem('realHost')
      this.project = await this.nativeStorage.getItem('project')
      this.settingInit = await this.nativeStorage.getItem('settingInit') === '1'
    } catch (e) {
      console.error(e)
      return
    }
    try {
      const res: any = await this.schoolService.getschool()
      console.log(res)
      this.schename = res.schename
      this.schcname = res.schcname
      this.logopath = res.logopath
    } catch (e) {
      console.error(e)
    }
  }

  toSetting() {
    this.router.navigate(['setting'])
  }
  toLogin() {
    this.router.navigate(['login'])
  }
  onLogoLoad() {
    console.log('onLogoLoad')
    this.logoClassName = 'sch-logo sch-logo-loaded'
  }
  onLogoError(e) {
    console.log('onLogoError', e)
    if (e.target.src) {
      e.target.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/wcAAwAB/krubgAAAABJRU5ErkJggg=='
      this.logoClassName = 'sch-logo sch-logo-error'
    }
  }
}
