
<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onAdd()">Add</ion-button>
    </ion-buttons>
    <ion-title>Note List</ion-title>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onBack()">Back</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-tab-bar slot="top">
  <!--<ion-button color="asset" (click)="onUpload()">Upload Data</ion-button>-->
  <ion-list style="width: 100%; height: 51px; margin-top: 22px;margin-bottom: 17px;">
    <ion-item class="header-row" lines="full" [button]="false">
      <span class="label">All Select</span>
      <ion-checkbox [(ngModel)]="allCheck" slot="end" (ngModelChange)="onCheckAll()"></ion-checkbox>
    </ion-item>
  </ion-list>
</ion-tab-bar>
<ion-content>
  <!--<ion-button (click)="test()">texs</ion-button>-->
  <ion-list>
    <!--<ion-item class="header-row" lines="full">-->
    <!--  <span class="label">All Select</span>-->
    <!--  <ion-checkbox [(ngModel)]="allCheck" slot="end"></ion-checkbox>-->
    <!--</ion-item>-->
    <ion-item-sliding *ngFor="let item of list">
      <ion-item button detail lines="full">
        <div class="info-box" (click)="onClick(item);" tappable>
          <span class="info-item no">Note No:{{ item.asset_check_no }}</span>
          <span class="info-item">Note Name:{{ item.asset_check_name }}</span>
          <span class="info-item">Location Name:{{ item.locationname }}</span>
        </div>

        <ion-checkbox [(ngModel)]="item.check" slot="end" (ngModelChange)="onChangeItem(item)"></ion-checkbox>

      </ion-item>
      <ion-item-options side="end">
        <ion-item-option color="danger" (click)="onDelete(item)">Delete</ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>
  <app-empty *ngIf="list.length === 0" [message]="'Please add new note.'" mode="dark"></app-empty>
</ion-content>

<ion-tab-bar slot="bottom">
  <ion-button color="asset" (click)="onUpload()" style="margin: 0 auto 0 0.5rem;">Upload Data</ion-button>
</ion-tab-bar>
