import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { BorrowSearchPage } from './borrow-search.page';

describe('BorrowSearchPage', () => {
  let component: BorrowSearchPage;
  let fixture: ComponentFixture<BorrowSearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BorrowSearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BorrowSearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
