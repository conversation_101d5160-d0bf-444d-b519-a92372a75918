import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FunctionPage } from './function.page';

describe('FunctionPage', () => {
  let component: FunctionPage;
  let fixture: ComponentFixture<FunctionPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FunctionPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FunctionPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
