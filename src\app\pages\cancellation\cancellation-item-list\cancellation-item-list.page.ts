import {ApplicationRef, Component, OnInit} from '@angular/core'
import {Platform, AlertController, NavController, ModalController} from '@ionic/angular'
import {ActivatedRoute, Router} from '@angular/router'
import {UtilsService} from '@app/utils/utils.service'
import {TreeSelectPage} from '@app/components/tree-select/tree-select.page'
import {CancellationService} from '@services/sql/cancellation.service'
import {Camera, CameraOptions} from '@ionic-native/camera/ngx'
import {RelocationService} from '@services/sql/relocation.service'
import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import {EventService} from '@services/event/event.service'
import {AssetService} from '@services/api/asset.service'
import {LoadingService} from '@app/utils/loading.service'

@Component({
  selector: 'app-cancellation-item-list',
  templateUrl: './cancellation-item-list.page.html',
  styleUrls: ['./cancellation-item-list.page.scss'],
})
export class CancellationItemListPage implements OnInit {

  public id = ''
  public data: any = {}
  public confirmation = true
  public locations: any = []
  public options: CameraOptions = {
    quality: 50,
    destinationType: this.camera.DestinationType.DATA_URL,
    encodingType: this.camera.EncodingType.JPEG,
    mediaType: this.camera.MediaType.PICTURE,
    correctOrientation: true,
    // sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
  }
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    private cancellationService: CancellationService,
    private relocationService: RelocationService,
    public asset: AssetService,
    private router: Router,
    private loading: LoadingService,
    private eventService: EventService,
    private alertController: AlertController,
    private nav: NavController,
    private modalController: ModalController,
    private utils: UtilsService,
    private app: ApplicationRef,
    private navCtrl: NavController,
    private camera: Camera,
    private photoViewer: PhotoViewer
  ) { }

  ngOnInit() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      this.id = this.routeInfo.snapshot.params.id
      this.init()
      this.loadLocations()
    })
  }
  async init() {
    try {
      const data = await this.cancellationService.get(this.id)
      console.log('cancellation-item-list init', data)
      this.data = data
    } catch (e) {
      console.error(e)
    }
  }

  async loadLocations() {
    try {
      const res = await this.relocationService.getLocation()
      this.locations = res
    } catch (e) {
      console.error(e)
    }
  }
  getLocation(list, no) {
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.no === no) {
        return item
      }
      if (item.child && item.child.length > 0) {
        const child = this.getLocation(item.child, no)
        if (child) {
          return child
        }
      }
    }
    return null
  }
  getLocationName(no) {
    const item = this.getLocation(this.locations, no)
    return item ? item.location_name : ''
  }

  get title() {
    if (this.data && this.data.asset_check_no) {
      return 'Note No:' + this.data.asset_check_no
    }
    return ''
  }

  get onLine() {
    return navigator.onLine
  }
  onNoteList() {
    this.nav.pop()
  }
  onSave() {
    this.saveData()
  }

  saveData() {
    const id = this.data.id
    const img = this.data.img
    const property_location_serial_no = this.data.property_location_serial_no
    const property_barcode = this.data.property_barcode
    const property_location = this.data.property_location
    const change_num = this.data.change_num
    const property_status_change_woreason = this.data.property_status_change_woreason
    const property_status_change_remark = this.data.property_status_change_remark
    const property_status = ''
    this.cancellationService.update({
      // tslint:disable-next-line:max-line-length
      id, img, property_location_serial_no, property_barcode, property_location,
      change_num, property_status_change_woreason, property_status_change_remark,
      property_status
    }).then(() => {
      this.eventService.emit('update_cancellation_list')
      this.nav.pop()
    })
  }
  getLocationArr(list, codes) {
    const arr = []
    for (const item of list) {
      if (codes.includes(item.no)) {
        arr.push(item)
      }
      if (Array.isArray(item.child) && item.child.length > 0) {
        const child = this.getLocationArr(item.child, codes)
        if (child.length > 0) {
          arr.push(...child)
        }
      }
    }
    return arr
  }

  async onSelectLocation() {
    // if (this.data.property_status !== 1) {
    //   return
    // }
    let list: unknown = []
    try {
      list = await this.relocationService.getLocation()
      if (Array.isArray(this.data.location_list) && this.data.location_list.length > 0) {
        list = this.getLocationArr(list, this.data.location_list)
      }
    } catch (e) {
      console.error(e)
    }
    const res: any = await this.utils.showModal(TreeSelectPage, {
      list,
      title: 'Choose Location',
      codeKey: 'no',
      nameKey: 'location_name',
    })
    console.log(res)
    if (res && res.data && res.data.save) {
      const data = res.data.data
      this.data.property_location = data.no
      this.app.tick()
    }
  }
  addImg() {
    if (this.data.img) {
      this.data.img = null
    } else {
      this.camera.getPicture(this.options).then((imageData) => {
        // imageData is either a base64 encoded string or a file URI
        // If it's base64 (DATA_URL):
        const base64Image = 'data:image/jpeg;base64,' + imageData;
        console.log(base64Image)
        this.data.img = base64Image
        // this.cancellationService.update({
        //   id: this.data.id,
        //   img: base64Image
        // })
      }, (err) => {
        // Handle error
        console.error(err)
      })
    }
  }
  imgViewer() {
    if (this.data.img) {
      this.photoViewer.show(this.data.img)
    }
  }
  removeImg() {
    this.data.img = null
  }

  async searchItem() {
    if (!this.onLine) {
      this.utils.showToast('Please check WIFI connection')
      return
    }
    const codeno = this.data.property_barcode
    await this.loading.start()
    let data = null
    let itemurl = ''
    try {
      data = await this.asset.getItemUrl(codeno)
      itemurl = data.itemurl
      console.log(data)
    } catch (err) {
      console.error(err)
      this.utils.showMsg('Barcode No. not Found')
      await this.loading.end()
      return
    }
    if (itemurl) {
      this.openItemDetail(codeno, itemurl)

    }

    await this.loading.end()
  }

  openItemDetail(codeno, itemurl) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['/item-detail/item'],
      {
        queryParams: {
          codeno,
          url: itemurl
        },
      }
    )
  }
}
