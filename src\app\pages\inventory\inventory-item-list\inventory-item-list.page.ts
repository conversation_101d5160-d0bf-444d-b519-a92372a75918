import { Component, OnInit } from '@angular/core';
import {Platform, AlertController, NavController, ModalController} from '@ionic/angular'
import {ActivatedRoute} from '@angular/router'
import {NoteListService} from '@services/sql/note-list.service'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ScannerPagePage} from '@pages/scanner-page/scanner-page.page'
import {InventoryScannerPage} from '@pages/inventory/inventory-scanner/inventory-scanner.page'
import {UtilsService} from '@app/utils/utils.service'
import {SelectLocationPage} from '@pages/inventory/select-location/select-location.page'
import {StorageNativeService} from '@services/storageNative/storage-native.service'

@Component({
  selector: 'app-inventory-item-list',
  templateUrl: './inventory-item-list.page.html',
  styleUrls: ['./inventory-item-list.page.scss'],
})
export class InventoryItemListPage implements OnInit {

  public id = ''
  public data: any = {}
  public confirmation = true
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    private noteListService: NoteListService,
    private alertController: AlertController,
    private nav: NavController,
    private modalController: ModalController,
    private utils: UtilsService,
    private nativeStorage: StorageNativeService,
  ) { }

  ngOnInit() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      this.nativeStorage.getItem('stockTakeConfirmation').then(v => {
        this.confirmation = v === '1'
      }).catch(() => {})
      this.id = this.routeInfo.snapshot.params.id
      this.init()
    })
  }
  async init() {
    try {
      const data = await this.noteListService.get(this.id)
      this.data = data
    } catch (e) {
      console.error(e)
    }
  }

  get title() {
    if (this.data && this.data.asset_check_no) {
      return 'Note No:' + this.data.asset_check_no
    }
    return ''
  }

  async onChangeLocation(item) {
    // 選擇地點
    console.log(item)
    const res: any = await this.utils.showModal(SelectLocationPage, {})
    console.log('返回結果', res)
    if (res.data && res.data.save && res.data.data) {
    //   id: "LABPR-5"
    //   isParent: "false"
    //   name: "5/F 實驗室預備室"
    //   orgName: "5/F 實驗室預備室"
    //   pid: "5"
    //   setname: "5/F 實驗室預備室"
    //   const list = this.data.list
      item.asset_match_locationid = res.data.data.id
      item.asset_match_locationname = res.data.data.name

      this.saveData()
    }

  }
  async onChangeSTQ(item) {
    let num = item.stocktakeqty
    if (Number(num) === 0) {
      num = ''
    }
    const title = 'Change Stock Take Qty'
    const msg = 'Name: ' + item.name + '<br>Property no: ' + item.property_no + '<br>Qty: ' + item.property_qty
    const alert = await this.alertController.create({
      header: title,
      message: msg,
      backdropDismiss: false,
      inputs: [
        {
          name: 'num',
          type: 'number',
          checked: true,
          placeholder: '0',
          value: num
        }
      ],
      // subHeader: '',
      buttons: [
        {
          text: 'CANCEL',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (data) => {
          }
        }, {
          text: 'CONFIRM',
          handler: (data) => {
            console.log(data)
            const n = Number(data.num)
            item.stocktakeqty = isNaN(n) ? 0 : n
            this.saveData()
          }
        }
      ]
    });
    await alert.present();
    await alert.onDidDismiss()
  }
  async onChangeAW(item) {
    let num = item.asset_check_match_w_numonNoteList
    if (Number(num) === 0) {
      num = ''
    }
    const title = 'Change Apply WO'
    const msg = 'Name: ' + item.name + '<br>Property no: ' + item.property_no + '<br>Qty: ' + item.property_qty
    const alert = await this.alertController.create({
      header: title,
      message: msg,
      backdropDismiss: false,
      inputs: [
        {
          name: 'num',
          type: 'number',
          checked: true,
          placeholder: '0',
          value: num
        }
      ],
      // subHeader: '',
      buttons: [
        {
          text: 'CANCEL',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (data) => {
          }
        }, {
          text: 'CONFIRM',
          handler: (data) => {
            console.log(data)
            const n = Number(data.num)
            item.asset_check_match_w_num = isNaN(n) ? 0 : n
            this.saveData()
          }
        }
      ]
    });
    await alert.present();
    await alert.onDidDismiss()
  }

  isAW(item) {
    let n = 0
    if (item && item.asset_check_match_w_num) {
      n = Number(item.asset_check_match_w_num)
      if (isNaN(n)) {
        return 0
      }
      return n
    }
    return n
  }

  onNoteList() {
    this.nav.pop()
  }
  async onScan() {
    const { text, type } = await this.showScanModal({
      validate: this.validate,
      page: this,
    })

    if (text) {

    }
  }
  validate(page, text, type) {
    console.log(page, text, type)
    return new Promise((resolve, reject) => {
      if (type === 'single') {
        page.addItemSingle(text).then(res => {
          resolve(res)
        }).catch(e => {
          reject(e)
        })
      } else {
        page.addItemBatch(text).then(res => {
          resolve(res)
        }).catch(e => {
          reject(e)
        })
      }
    })
  }

  async addItemSingle(text) {
    console.log('addItemSingle', text)
    if (this.listHasNo(text)) {
      const list = this.data.list
      let tmpIndex = list.findIndex(i => i.pno === text)
      let item
      if (tmpIndex > -1) {
        item = list.find(i => i.pno === text)
      } else {
        item = list.find(i => i.property_no === text)
      }

      if (item) {
        const n = Number(item.stocktakeqty)
        item.stocktakeqty = n + 1
        this.data.list = list.map(i => Object.assign({}, i))
        this.saveData()
        return true
      }
    } else {
      if (await this.utils.confirm(
        'Warning',
        `Code No: ${text} does not exist, add in ?`,
        'CONFIRM',
        'CANCEL')) {

        const title = 'Change Stock Take Qty'
        const msg = 'Name: ' + text + '<br>Property no: ' + text + '<br>Qty: 0'
        const qty = await this.inputQty(title, msg, 0)
        if (qty) {
          this.addItem(text, qty)
        }
        return true
      }

    }
    return false
  }

  async addItemBatch(text) {
    console.log('addItemBatch', text)
    if (this.listHasNo(text)) {
      const list = this.data.list
      let tmpIndex = list.findIndex(i => i.pno === text)
      let item
      if (tmpIndex > -1) {
        item = list.find(i => i.pno === text)
      } else {
        item = list.find(i => i.property_no === text)
      }

      const title = 'Change Stock Take Qty'
      const msg = 'Name: ' + text + '<br>Property no: ' + text + '<br>Qty: ' + item.stocktakeqty
      const qty = await this.inputQty(title, msg, 0)
      if (qty) {
        item.stocktakeqty = qty
        this.data.list = list.map(i => Object.assign({}, i))
        this.saveData()
      }

      return true
    } else {

      if (await this.utils.confirm(
        'Warning',
        `Code No: ${text} does not exist, add in ?`,
        'CONFIRM',
        'CANCEL')) {

        const title = 'Change Stock Take Qty'
        const msg = 'Name: ' + text + '<br>Property no: ' + text + '<br>Qty: 0'
        const qty = await this.inputQty(title, msg, 0)
        if (qty) {
          this.addItem(text, qty)
        }
        return true
      }
    }
    return false
  }

  listHasNo(no) {
    if (this.data.list.findIndex(i => i.pno === no) === -1) {
      return this.data.list.findIndex(i => i.property_no === no) !== -1
    } else {
      return true
    }
  }
  addItem(text, qty = '0') {
    this.data.list.push({
      property_location_serial_no: text,
      property_location_serial_serial: text,
      pno: text,
      property_no: text,
      name: text,
      property_qty: '0',
      stocktakeqty: qty,
      asset_check_match_w_num: '0',
      old_location_id: '',
      asset_check_match_location_id: '',
      isrecheck: '0',
      property_location_serial_jstm: text,
      property_barcode: text,
      locationid: '',
      locationname: '',
      asset_match_locationid: '',
      asset_match_locationname: '',
      note_no: this.data.asset_check_no
    })
    this.saveData()
  }

  async showScanModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: InventoryScannerPage,
      componentProps: {
        ...props,
        id: 'item-scanner-modal'
      },
      id: 'item-scanner-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    return res.data
  }



  async inputQty(title, msg, defaultValue) {
    let num = defaultValue
    if (Number(num) === 0) {
      num = ''
    }
    const alert = await this.alertController.create({
      header: title,
      message: msg,
      backdropDismiss: false,
      inputs: [
        {
          name: 'num',
          type: 'number',
          checked: true,
          placeholder: '0',
          value: num
        }
      ],
      // subHeader: '',
      buttons: [
        {
          text: 'CANCEL',
          role: 'cancel',
          cssClass: 'secondary',
          handler: (data) => {
          }
        }, {
          text: 'CONFIRM',
          handler: (data) => {
            console.log(data)
            if (data && data.num !== '') {
              num = Number(data.num)
            } else {
              this.utils.showToast('Input cannot be null')
            }
          }
        }
      ]
    });
    await alert.present();
    await alert.onDidDismiss()
    return num
  }

  async onConfirm(item) {
    console.log('onConfirm', item)
    let confirm = true
    if (this.confirmation) {
      confirm = false
      if (await this.utils.confirm('Warning', 'Confirm ?', 'CONFIRM', 'CANCEL')) {
        console.log('confirm')
        confirm = true
      }
    }
    if (!confirm) {
      return
    }
    item.stocktakeqty = item.property_qty
    this.saveData()
  }

  saveData() {
    const id = this.data.id
    const asset_check_no = this.data.asset_check_no
    const asset_check_name = this.data.asset_check_name
    const locationname = this.data.locationname
    const is_submit = this.data.is_submit
    const list = this.data.list
    this.noteListService.update({
      id, asset_check_no, asset_check_name, locationname, is_submit, list
    })
  }
}
