import { Component, OnInit } from '@angular/core';
import {Subscription} from 'rxjs'
import {ActivatedRoute, Router, NavigationEnd} from '@angular/router'
import {NavController, Platform} from '@ionic/angular'
import {ThemeableBrowser} from '@ionic-native/themeable-browser/ngx'
import {DomSanitizer} from '@angular/platform-browser'

@Component({
  selector: 'app-item-detail-maint',
  templateUrl: './item-detail-maint.page.html',
  styleUrls: ['./item-detail-maint.page.scss'],
})
export class ItemDetailMaintPage implements OnInit {

  public url: any = ''
  private subscription: Subscription;
  constructor(
    private router: Router,
    // private browserTab: BrowserTab
    private routeInfo: ActivatedRoute,
    private platform: Platform,
    private themeableBrowser: ThemeableBrowser,
    private sanitizer: DomSanitizer,
    private nav: NavController,
  ) {
  }

  ngOnInit() {

    this.platform.ready().then(() => {
      // this.url = this.sanitizer.bypassSecurityTrustResourceUrl('https://www.baidu.com/');
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.url = this.routeInfo.snapshot.queryParams.url

      this.subscription = this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd ) {
          if (event instanceof NavigationEnd && event.url.indexOf('/item-detail/maint') >= 0) {
            this.init();
          }
        }
      });

    })
  }
  onClose() {
    this.nav.pop();
  }

  init() {
    const url = this.routeInfo.snapshot.queryParams.url
    this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }


  ionViewDidEnter() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      const url = this.routeInfo.snapshot.queryParams.url
      this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);
    })
  }

}
