import {Component, OnInit} from '@angular/core'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ScannerPagePage} from '@pages/scanner-page/scanner-page.page'
import {ModalController, NavController} from '@ionic/angular'
import {AssetService} from '@services/api/asset.service'
import {UtilsService} from '@app/utils/utils.service'
import {LoadingService} from '@app/utils/loading.service'
import {Router} from '@angular/router'
import {TreeSelectPage} from '@app/components/tree-select/tree-select.page'
import {RelocationService} from '@services/sql/relocation.service'
import {EventService} from '@services/event/event.service'

@Component({
  selector: 'app-borrow-search',
  templateUrl: './borrow-search.page.html',
  styleUrls: ['./borrow-search.page.scss'],
})
export class BorrowSearchPage implements OnInit {

  public noteNo = ''
  constructor(
    private modalController: ModalController,
    private asset: AssetService,
    private utils: UtilsService,
    private nav: NavController,
    private loading: LoadingService,
    private router: Router,
    private eventService: EventService,
    private relocationService: RelocationService,
  ) {
    this.eventService.event.on('borrow-clear', () => {
      this.noteNo = ''
    })
  }

  ngOnInit() {
  }
  get onLine() {
    return navigator.onLine
  }
  onBack() {
    // this.modalController.dismiss({
    //   cancel: true
    // })
    this.nav.pop()
  }
  async onScan() {
    const text = await this.showScanModal({})
    if (text) {
      this.noteNo = text
      this.handleSearch(text)
    }
  }
  async onConfirm() {
    const text = this.noteNo
    this.handleSearch(text)

    // this.nav.setDirection('forward');
    // this.router.navigate(
    //   ['borrow-item-list'],
    // )
  }



  async showScanModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ScannerPagePage,
      componentProps: {
        ...props,
        id: 'list-scanner-modal'
      },
      id: 'list-scanner-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    return res.data && res.data.text || ''
  }


  // tslint:disable-next-line:no-unnecessary-initializer
  async checkNo(checkno, property_location = undefined) {
    try {
      return await this.asset.getAppRelocationInfo(checkno, property_location)
    } catch (e) {
      console.error(e)
      return e
    }
    return false
  }
  // tslint:disable-next-line:no-unnecessary-initializer
  async handleSearch(text, location = undefined) {
    if (typeof text !== 'string' || text.length <= 0) {
      return
    }
    if (!this.onLine) {
      this.utils.showMsg('Please check WIFI connection')
      return
    }
    await this.loading.start()
    const data = await this.checkNo(text, location)
    if (data) {
      await this.loading.end()
      if ((!data.status || data.status === 200)) {
        this.toPage(text, data)
        return
      } else if (data.status === 422) {
        const localtion = await this.onSelectLocation(data.location_list)
        console.log(localtion)
        if (localtion) {
          this.handleSearch(text, localtion.no)
          // const data2 = await this.checkNo(text, localtion.no)
          // data.relocation_location_no = localtion.no
          // this.modalController.dismiss({
          //   save: true,
          //   text,
          //   info: data,
          // })
          return
        }
      }
      this.utils.showMsg('Enquiry Register No. not Found')
    } else {
      await this.loading.end()
      switch (data.status) {
        case 404:
          this.utils.showMsg('No Access Right')
          break
        default:
          this.utils.showMsg('Enquiry Register No. not Found')
          break
      }
    }
  }

  async onSelectLocation(codes) {
    let list: unknown = []
    try {
      list = await this.relocationService.getLocation()
      list = this.relocationService.getLocationArray(list, codes)
    } catch (e) {
      console.error(e)
    }
    const res: any = await this.utils.showModal(TreeSelectPage, {
      list,
      title: 'Choose Location',
      codeKey: 'no',
      nameKey: 'location_name',
    })
    console.log(res)
    if (res && res.data && res.data.save) {
      const data = res.data.data
      return data
    }
    return null
  }
  async getNoteList(checkno) {
    try {
      return await this.asset.getItemList(checkno)
    } catch (e) {
      console.error(e)
    }
    return false
  }

  toPage(barcode, data) {
    this.router.navigate(
      ['/borrow-item-list'],
      {
        queryParams: {
          barcode,
          data: JSON.stringify(data)
        },
      }
    )
  }
}
