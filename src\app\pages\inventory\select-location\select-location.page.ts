import {Component, Input, OnInit} from '@angular/core'
import {ModalController} from '@ionic/angular'
import {AssetService} from '@services/api/asset.service'
import {UtilsService} from '@app/utils/utils.service'
import {LoadingService} from '@app/utils/loading.service'

@Component({
  selector: 'app-select-location',
  templateUrl: './select-location.page.html',
  styleUrls: ['./select-location.page.scss'],
})
export class SelectLocationPage implements OnInit {
  @Input() parentId = '0'
  public list: any = []
  constructor(
    private modal: ModalController,
    private asset: AssetService,
    private utils: UtilsService,
    private loading: LoadingService,
  ) { }

  ngOnInit() {
    this.init()
  }
  onCancel() {
    this.modal.dismiss({
      cancel: true
    })
  }


  async init() {
    await this.loading.start()
    try {
      const res = await this.asset.getLocation(this.parentId)
      console.log(res)
      this.list = res
    } catch (e) {
      console.error(e)
    }
    await this.loading.end()
  }

  onClickItem(item) {
    if (item.isParent === 'true') {
      this.selectChild(item.id)
    } else {
      console.log('選擇地址：', item)
      this.modal.dismiss({
        save: true,
        data: item
      })
    }
  }
  async selectChild(id) {
    const res: any = await this.utils.showModal(SelectLocationPage, {parentId: id})
    if (res.data.save) {
      this.modal.dismiss({
        save: true,
        data: res.data.data
      })
    }
  }
}
