<!--<ion-header>-->
<!--  <ion-toolbar>-->
<!--    <ion-title>itemDetail</ion-title>-->
<!--  </ion-toolbar>-->
<!--</ion-header>-->

<ion-tabs>
  <ion-tab-bar slot="bottom">
    <ion-tab-button (click)="routeToTab('item')" [selected]="selectedTab('item')">
      <!--<ion-icon name="calendar"></ion-icon>-->
      <ion-label>Item</ion-label>
      <!--<ion-badge>6</ion-badge>-->
    </ion-tab-button>

    <ion-tab-button (click)="routeToTab('access')" [selected]="selectedTab('access')">
      <!--<ion-icon name="person-circle"></ion-icon>-->
      <ion-label>Access</ion-label>
    </ion-tab-button>

    <ion-tab-button (click)="routeToTab('maint')" [selected]="selectedTab('maint')">
      <!--<ion-icon name="map"></ion-icon>-->
      <ion-label>Maint</ion-label>
    </ion-tab-button>

    <ion-tab-button (click)="routeToTab('other')" [selected]="selectedTab('other')">
      <!--<ion-icon name="information-circle"></ion-icon>-->
      <ion-label>Other</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>
