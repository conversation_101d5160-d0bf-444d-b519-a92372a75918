import { Component, OnInit } from '@angular/core';
// import { BrowserTab } from '@ionic-native/browser-tab/ngx';
import { ThemeableBrowser, ThemeableBrowserOptions, ThemeableBrowserObject } from '@ionic-native/themeable-browser/ngx';
import {Platform, NavController} from '@ionic/angular'
import {ActivatedRoute, Router, NavigationEnd} from '@angular/router';
import { Subscription } from 'rxjs';
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';
import {UtilsService} from '@app/utils/utils.service'
@Component({
  selector: 'app-item-detail-item',
  templateUrl: './item-detail-item.page.html',
  styleUrls: ['./item-detail-item.page.scss'],
})
export class ItemDetailItemPage implements OnInit {

  public style = 'height: 100%;width: 100%;';
  public url: SafeResourceUrl;
  private subscription: Subscription;
  constructor(
    private router: Router,
    // private browserTab: BrowserTab
    private routeInfo: ActivatedRoute,
    private platform: Platform,
    private themeableBrowser: ThemeableBrowser,
    private sanitizer: DomSanitizer,
    private nav: NavController,
    private utils: UtilsService,
  ) {
    this.url = this.sanitizer.bypassSecurityTrustResourceUrl('about:blank')
  }

  ngOnInit() {

    this.platform.ready().then(() => {
      // this.url = this.sanitizer.bypassSecurityTrustResourceUrl('https://www.baidu.com/');
      // this.statusBar.backgroundColorByHexString('#003658')
      // this.url = this.routeInfo.snapshot.queryParams.url

      this.subscription = this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd ) {
          if (event instanceof NavigationEnd && event.url.indexOf('/item-detail/item') >= 0) {
            this.init();
          }
        }
      });

    })
  }
  onClose() {
    this.nav.pop();
    // this.nav.navigateBack('function')
  }

  init() {
    setTimeout(async () => {
      const url = this.routeInfo.snapshot.queryParams.url
      // await this.utils.showMsg(url)
      // await this.utils.showMsg(this.sanitizer.bypassSecurityTrustResourceUrl(url) + '')
      // this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);
      this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);
      // this.sanitizer.sanitize(SecurityContext.HTML, this.domSanitizer.bypassSecurityTrustHtml(this.parishDetail.mass_timings));

    }, 50)

  }


  ionViewDidEnter() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      this.init()
    })
  }

}
