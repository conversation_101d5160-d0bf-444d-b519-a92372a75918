import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { InventoryListPage } from './inventory-list.page';
import { SelectLocationPageModule} from '@pages/inventory/select-location/select-location.module'

import { EmptyModule } from '@app/components/empty/empty.module'

const routes: Routes = [
  {
    path: '',
    component: InventoryListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    SelectLocationPageModule,
    EmptyModule,
  ],
  declarations: [InventoryListPage]
})
export class InventoryListPageModule {}
