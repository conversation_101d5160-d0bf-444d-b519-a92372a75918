import {Component, OnInit} from '@angular/core'
import {UtilsService} from '@app/utils/utils.service'
import {SelectLocationPage} from '@pages/inventory/select-location/select-location.page'
import {AssetService} from '@services/api/asset.service'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ModalController, NavController} from '@ionic/angular'
import {InventorySearchPage} from '@pages/inventory/inventory-search/inventory-search.page'
import {NoteListService} from '@services/sql/note-list.service'
import {Router} from '@angular/router'
import {StorageNativeService} from '@services/storageNative/storage-native.service'

@Component({
  selector: 'app-inventory-list',
  templateUrl: './inventory-list.page.html',
  styleUrls: ['./inventory-list.page.scss'],
})
export class InventoryListPage implements OnInit {

  public list: any = []
  public allCheck = false
  public isInit = false
  constructor(
    private utils: UtilsService,
    private asset: AssetService,
    private modalController: ModalController,
    private nav: NavController,
    private noteList: NoteListService,
    private router: Router,
    private nativeStorage: StorageNativeService,
  ) { }

  async ngOnInit() {
    // await this.noteList.clear()
    const that = this
    await this.loadData(() => {
      if (that.list.length > 0) {
        that.isInit = true
        that.showPage()
      } else {
        this.hidePage()
        that.onAdd()
      }
    })
  }
  onBack() {
    console.log(this.list, 1)
    // this.noteList.clear()
    this.nav.pop()
    console.log(this.list, 2)
  }
  hidePage() {
    // @ts-ignore
    document.getElementsByTagName('app-inventory-list')[0].style.display = 'none'
  }
  showPage() {
    setTimeout(() => {
    // @ts-ignore
    document.getElementsByTagName('app-inventory-list')[0].style.display = ''
    }, 1500)
  }

  loadData(callback = undefined) {
    this.list = []
    this.noteList.all().then(res => {
      this.list = res
      if (callback !== undefined) {
        callback()
      }
    }).catch(err => {
      console.error(err)
    })
  }
  async onAdd() {
    console.log(this.list, 0)
    const data = await this.showSearchModal({isInit: this.isInit, list: this.list})
    if (data && data.asset_check_no) {
      this.isInit = true
      // this.list.push(data)
      let isExist = false
      try {
        const item = await this.noteList.getByNo(data.asset_check_no)
        if (item) {
          isExist = true
        }
      } catch (e) {
      }
      this.showPage()
      if (isExist) {
        return
      }
      try {
        await this.noteList.add(data.asset_check_no, data.asset_check_name, data.locationname, data.is_submit, data.list)

      } catch (e) {
        console.error(e)
      }
    } else {
      if (data && data.redirect) {
        this.showPage()
      } else {
        if (!this.isInit) {
          this.onBack()
        } else {
          this.showPage()
        }
      }
    }
    if (this.isInit) {
    //   this.showPage()
      await this.loadData()
    }
  }

  async test() {
    // 選擇地點
    const res: any = await this.utils.showModal(SelectLocationPage, {})
    console.log('返回結果', res)
  }

  async checkNo(checkno) {
    try {
      const res = await this.asset.checkListNo(checkno)
      return true
    } catch (e) {
      console.error(e)
    }
    return false
  }


  async showSearchModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: InventorySearchPage,
      componentProps: {
        ...props,
        id: 'inventory-search-modal'
      },
      id: 'inventory-search-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data && res.data.save && res.data.info) {
      return {
        ...res.data.info,
        list: res.data.list,
      }
    }
    return {}
  }

  onDelete(item) {
    console.log(item)
    this.noteList.delete(item.id)
    // const i = this.list.findIndex(i => i.id === item.id )
    this.loadData()
  }
  onClick(item) {
    console.log(item)

    this.nav.setDirection('forward');
    this.router.navigate(
      ['inventory-item-list', item.id],
    )
  }

  async onUpload() {
    if (!await this.utils.confirm('Warning', 'Confirm the upload?', 'CONFIRM', 'CANCEL')) {
      return
    }
    const list = this.list.filter(i => i.check)
    if (list.length > 0) {
      console.log(list)
      let user_id = ''
      try {
        user_id = await this.nativeStorage.getItem('user_id')
      } catch (e) {
        console.log('無user_id')
        this.utils.showMsg('Timeout, Please re-login')
        return
      }
      const is_submit = '1'
      const stocktakedata = {}
      for (const rowItem of list) {
        const item: any = await this.noteList.get(rowItem.id)
        stocktakedata[item.asset_check_no] = item.list.map(i => {
          return {
            property_location_serial_no: i.property_location_serial_no || '',
            property_location_serial_serial: i.property_location_serial_serial || '',
            property_no: i.property_no || '',
            pno: i.pno || '',
            name: i.name || '-',
            property_qty: i.property_qty || '0',
            stocktakeqty: i.stocktakeqty || '0',
            asset_check_match_w_num: i.asset_check_match_w_num || '0',
            old_location_id: i.old_location_id || '0',
            asset_check_match_location_id: i.old_location_id || '',
            isrecheck: i.isrecheck || '0',
            property_location_serial_jstm: i.property_location_serial_jstm || '0',
            property_barcode: i.property_barcode || '',
            locationid: i.locationid || '',
            locationname: i.locationname || '',
            asset_match_locationid: i.asset_match_locationid || '',
            asset_match_locationname: i.asset_match_locationname || '',
            note_no: item.asset_check_no
          }
        })
      }
      try {
        const res = await this.asset.saveStockTakeList(
          JSON.stringify(stocktakedata),
          user_id,
          is_submit)
        console.log(res)
        debugger

      } catch (e) {
        console.error(e)
      }
      for (const item of list) {
        await this.noteList.delete(item.id)
      }
      this.loadData()
      this.utils.showToast('Upload complete!')
    }
  }
  onCheckAll() {

    // const check = this.list.event(i => i.check)
    this.list.forEach(i => {
      i.check = this.allCheck
    })
  }
  onChangeItem(item) {
    this.allCheck = this.list.every(i => i.check)
  }
}
