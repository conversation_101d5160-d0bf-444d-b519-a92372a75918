<ion-item *ngIf="data" lines="none" button (click)="toDetail()">
  <ion-thumbnail class="info-image" slot="start">
<!--    <img src="assets/images/logo.png">-->
    <ion-img [src]="imgUrl" (ionError)="imgError()"></ion-img>
  </ion-thumbnail>
  <ion-label class="info-text">
    <h3 class="info-title">
      {{ data.title }}
    </h3>
    <p class="info-content ion-text-wrap">
      {{ content }}
    </p>
  </ion-label>
</ion-item>
<ion-item *ngIf="!data" lines="none">
  <ion-thumbnail class="info-image" slot="start">
    <ion-skeleton-text animated></ion-skeleton-text>
  </ion-thumbnail>
  <ion-label class="info-text">
    <h3>
      <ion-skeleton-text animated style="width: 50%"></ion-skeleton-text>
    </h3>
    <p>
      <ion-skeleton-text animated style="width: 80%"></ion-skeleton-text>
    </p>
  </ion-label>
</ion-item>
