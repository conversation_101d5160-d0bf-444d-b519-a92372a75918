
<div class="ion-page" id="main-page">
<ion-header>
  <ion-toolbar>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onAdd()">Add</ion-button>
    </ion-buttons>
    <ion-title>W/O Apply</ion-title>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onBack()">Back</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<!--<ion-tab-bar slot="top">-->
<!--  &lt;!&ndash;<ion-button color="asset" (click)="onUpload()">Upload Data</ion-button>&ndash;&gt;-->
<!--  <ion-list style="width: 100%">-->
<!--    <ion-item class="header-row" lines="full" [button]="false">-->
<!--      <span class="label">All Select</span>-->
<!--      <ion-checkbox [(ngModel)]="allCheck" slot="end" (ngModelChange)="onCheckAll()"></ion-checkbox>-->
<!--    </ion-item>-->
<!--  </ion-list>-->
<!--</ion-tab-bar>-->
<ion-content>
  <!--<ion-button (click)="test()">texs</ion-button>-->
  <ion-list>
    <!--<ion-item class="header-row" lines="full">-->
    <!--  <span class="label">All Select</span>-->
    <!--  <ion-checkbox [(ngModel)]="allCheck" slot="end"></ion-checkbox>-->
    <!--</ion-item>-->
    <ion-item-sliding *ngFor="let item of list">
      <!--<ion-item button detail lines="full">-->
      <!--  <div class="info-box" (click)="onClick(item);" tappable>-->
      <!--    <span class="info-item no">Barcode:{{ item.property_barcode }}</span>-->
      <!--    <span class="info-item">Name:{{ item.property_name }}</span>-->
      <!--    <span class="info-item">Location Name:{{ item.location_name }}</span>-->
      <!--  </div>-->

      <!--  &lt;!&ndash;<ion-checkbox [(ngModel)]="item.check" slot="end" (ngModelChange)="onChangeItem(item)"></ion-checkbox>&ndash;&gt;-->

      <!--</ion-item>-->
      <!--<ion-item-options side="end">-->
      <!--  <ion-item-option color="danger" (click)="onDelete(item)">Delete</ion-item-option>-->
      <!--</ion-item-options>-->
      <ion-item button detail lines="full" (click)="onClick(item);">
        <div class="info-box" tappable>
          <span class="info-item no">Bar Code No.: {{ item.property_barcode }}</span>
          <span class="info-item">Name: {{ item.property_name }}</span>
          <span class="info-item">Location: {{ getLocationName(item.property_location) }}</span>
          <span class="info-item">Status: <ion-text [color]="getStatusColor(item)">{{ getStatus(item) }}</ion-text></span>
        </div>
        <div slot="end" class="img-box" tappable>
          <div class="img-wrapper">
            <img *ngIf="item.img" [src]="item.img" alt="" (click)="imgViewer($event, item)">
<!--            <div class="add-img"><ion-icon icon="image" ></ion-icon></div>-->
          </div>
        </div>

        <!--<ion-checkbox [(ngModel)]="item.check" slot="end" (ngModelChange)="onChangeItem(item)"></ion-checkbox>-->

      </ion-item>
      <ion-item-options side="end">
        <ion-item-option color="danger" (click)="onDelete(item)">Delete</ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>
  <app-empty *ngIf="list.length === 0" [message]="'Please add new cancellation.'" mode="dark"></app-empty>
</ion-content>
<ion-toolbar slot="bottom">
  <ion-button color="asset" (click)="onUpload()" style="margin: 0 auto 0 0.5rem;">Upload Data</ion-button>
</ion-toolbar>
<!--<ion-tabs>-->
<!--</ion-tabs>-->

</div>
