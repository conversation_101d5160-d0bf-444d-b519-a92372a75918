{"extends": "tslint:recommended", "rulesDirectory": ["codelyzer"], "rules": {"semicolon": [2, "never"], "variable-name": {"options": ["ban-keywords", "check-format", "allow-leading-underscore", "allow-pascal-case", "allow-snake-case"]}, "array-type": false, "arrow-parens": false, "deprecation": {"severity": "warn"}, "import-blacklist": [true, "rxjs/Rx"], "interface-name": false, "max-classes-per-file": false, "max-line-length": [true, 140], "member-access": false, "member-ordering": [true, {"order": ["static-field", "instance-field", "static-method", "instance-method"]}], "no-consecutive-blank-lines": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-empty": false, "no-inferrable-types": [true, "ignore-params"], "no-non-null-assertion": true, "no-redundant-jsdoc": true, "no-switch-case-fall-through": true, "no-use-before-declare": true, "no-var-requires": false, "object-literal-key-quotes": [true, "as-needed"], "object-literal-sort-keys": false, "ordered-imports": false, "quotemark": [true, "single"], "trailing-comma": false, "no-output-on-prefix": true, "no-inputs-metadata-property": true, "no-host-metadata-property": true, "no-input-rename": true, "no-output-rename": true, "use-lifecycle-interface": true, "use-pipe-transform-interface": true, "one-variable-per-declaration": false, "component-class-suffix": [true, "Page", "Component"], "directive-class-suffix": true, "directive-selector": [true, "attribute", "app", "camelCase"], "component-selector": [true, "element", "app", "page", "kebab-case"]}}