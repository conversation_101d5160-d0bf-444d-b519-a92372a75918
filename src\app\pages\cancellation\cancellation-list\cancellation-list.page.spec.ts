import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CancellationListPage } from './cancellation-list.page';

describe('CancellationListPage', () => {
  let component: CancellationListPage;
  let fixture: ComponentFixture<CancellationListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CancellationListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CancellationListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
