import {Component, Input, OnInit} from '@angular/core'
import {QRScanner, QRScannerStatus} from '@ionic-native/qr-scanner/ngx'
import {Alert<PERSON>ontroller, ModalController, NavController, Platform, ToastController} from '@ionic/angular'
import {ActivatedRoute, Router} from '@angular/router'
import {Location} from '@angular/common'
import {sleep} from '../../utils'
import {Vibration} from '@ionic-native/vibration/ngx'
import {TranslateService} from '@ngx-translate/core'
import {NativeAudio} from '@ionic-native/native-audio/ngx'
import {Brightness} from '@ionic-native/brightness/ngx'

@Component({
  selector: 'app-scanner-page',
  templateUrl: './scanner-page.page.html',
  styleUrls: ['./scanner-page.page.scss'],
})
export class ScannerPagePage implements OnInit {

  public success = false
  @Input() public scanText = ''
  @Input() public scanKey = ''
  @Input() public title = ''
  @Input() public tips = ''
  @Input() public page: any
  @Input() public validate: any
  @Input() public id = ''
  @Input() public showConfirm = false
  public needConfirm = false
  light: boolean // 判断闪光灯
  frontCamera: boolean // 判断摄像头
  isShow = false // 控制显示背景，避免切换页面卡顿

  public oldBrightness = 1
  public initBrightness = false

  constructor(
    private platform: Platform,
    private qrScanner: QRScanner,
    private toastCtrl: ToastController,
    private navCtrl: NavController,
    // @Inject(NavParams) private navParams: NavParams,
    private routeInfo: ActivatedRoute,
    private router: Router,
    private location: Location,
    private modalController: ModalController,
    private alertController: AlertController,
    private vibration: Vibration,
    public translate: TranslateService,
    private nativeAudio: NativeAudio,
    private brightness: Brightness,
  ) {
  }

  ngOnInit() {
    this.needConfirm = this.showConfirm
    this.platform.ready().then(() => {
      // 打印所有參數
      console.log('scanner page init', this.routeInfo.snapshot.queryParams)
      // this.scanKey = this.routeInfo.snapshot.queryParams.scanKey
      // this.title = this.routeInfo.snapshot.queryParams.title
      // this.tips = this.routeInfo.snapshot.queryParams.tips
      this.nativeAudio.preloadSimple('scan', 'assets/scan.mp3').then(res => {
        console.log(res)
      }).catch(err => {
        console.log(err)
      })
    })
    // this.showCamera()
  }

  ionViewDidLoad() {
  }

  ionViewDidEnter() {
    // 页面可见时才执行

    this.platform.ready().then(() => {
      this.showCamera()
      this.init()
      this.brightness.getBrightness().then(res => {
        this.oldBrightness = res
        this.initBrightness = true
        this.brightness.setBrightness(1)
      })
      this.isShow = true // 显示背景
    })
  }

  ionViewWillLeave() {
    this.hideCamera()
    if (this.initBrightness) {
      this.brightness.setBrightness(this.oldBrightness)
    }
  }

  init() {

    this.qrScanner.prepare()
      .then((status: QRScannerStatus) => {
        if (status.authorized) {
          // camera permission was granted
          // start scanning

          this.scanSub()
          this.qrScanner.resumePreview()
          // show camera preview
          this.qrScanner.show()
            .then((data: QRScannerStatus) => {
              // alert(data.showing);
            }, err => {
              this.showToast(err)
            })
        } else if (status.denied) {
          this.showToast(this.translate.instant('SCANNER.MESSAGE.SCANNING_ERROR'))
        } else {
          this.showToast(this.translate.instant('SCANNER.MESSAGE.NO_PERMISSION'))
        }
      })
      .catch((e: any) => console.log('Error is', e))
  }

  scanSub() {
    const scanSub = this.qrScanner.scan().subscribe((text: string) => {
      this.vibration.vibrate(50)
      this.nativeAudio.play('scan').then(res => {
        console.log(res)
      }).catch(err => {
        console.log(err)
      })
      scanSub.unsubscribe()

      this.success = true
      this.scanText = text
      if (this.validate) {
        this.validate(this.page, text, this.needConfirm).then(async (res: any) => {
          if (typeof res === 'object') {
            const {isOK, msg} = res
            if (isOK) {
              this.handleClose()
            } else {
              this.scanText = ''
              if (msg) {
                const alert = await this.alertController.create({
                  // header: '掃碼失敗',
                  subHeader: this.translate.instant('SCANNER.MESSAGE.SCANNING_FAILED'),
                  backdropDismiss: false,
                  message: msg,
                  buttons: [
                    {
                      text:  this.translate.instant('SCANNER.BUTTON.CANCEL'),
                      handler: () => {
                        this.handleClose()
                      },
                    }, {
                      text: this.translate.instant('SCANNER.BUTTON.RETRY'),
                      handler: async () => {
                        await sleep(500)
                        this.scanSub()
                      },
                    }],
                })
                await alert.present()
              } else {

                await sleep(500)
                this.scanSub()
              }

            }
          } else {
            if (res) {
              this.handleClose()
            } else {
              await sleep(500)
              this.scanSub()
            }
          }
        }).catch(err => {
          this.showToast(err.toString())
        })
      } else {
        this.handleClose()
      }
    })
  }

  /**
   * 闪光灯控制，默认关闭
   */
  toggleLight() {
    if (this.light) {
      this.qrScanner.disableLight()
    } else {
      this.qrScanner.enableLight()
    }
    this.light = !this.light
  }

  /**
   * 前后摄像头互换
   */
  toggleCamera() {
    if (this.frontCamera) {
      this.qrScanner.useBackCamera()
    } else {
      this.qrScanner.useFrontCamera()
    }
    this.frontCamera = !this.frontCamera
  }

  async showToast(msg) {
    const toast = await this.toastCtrl.create({
      message: msg,
      duration: 2000,
      // position: 'middle'
      position: 'bottom',
    })
    toast.present()
  }

  showCamera() {
    // @ts-ignore
    window.document.querySelector('#' + this.id ).classList.add('show-camera')
    // window.document.querySelectorAll(`ion-modal:not(#${this.id})`).forEach((i: HTMLElement) => { i.style.display = 'none'; i.style.opacity = '0' })
    window.document.querySelectorAll(`ion-modal:not(#${this.id})`).forEach((i: HTMLElement) => { i.classList.add('camera-hidden') })
    // @ts-ignore
    // @ts-ignore
    // window.document.querySelector('ion-app').style.visibility = 'hidden'
    // window.document.querySelector('app-scanner-page').style.visibility = 'visible'
    // window.document.querySelector('.modal-wrapper').style.background = 'transparent'
    // @ts-ignore
    window.document.querySelector('ion-app ion-router-outlet').style.opacity = '0'
    window.document.querySelector('.scanner-content').classList.add('show')
    window.document.querySelector('ion-app').classList.add('cameraView')
  }

  async hideCamera() {

    try {
      // @ts-ignore
      window.document.querySelector('#' + this.id ).classList.remove('show-camera')
      // window.document.querySelector('#' + this.id + ' .modal-wrapper').style.background = ''
      // window.document.querySelectorAll(`ion-modal:not(#${this.id})`).forEach((i: HTMLElement) => { i.style.display = ''; i.style.opacity = '' })
      window.document.querySelectorAll(`ion-modal:not(#${this.id})`).forEach((i: HTMLElement) => { i.classList.remove('camera-hidden') })
      // @ts-ignore
      // window.document.querySelector('.modal-wrapper').style.background = 'transparent'
      // @ts-ignore
      // window.document.querySelector('app-scanner-page').style.visibility = ''
      // window.document.querySelector('ion-app').style.visibility = ''
      // @ts-ignore
      window.document.querySelector('ion-app ion-router-outlet').style.opacity = ''
      window.document.querySelector('.scanner-content').classList.remove('show')
      window.document.querySelector('ion-app').classList.remove('cameraView')

      await this.qrScanner.hide() // 需要关闭扫描，否则相机一直开着
      await this.qrScanner.destroy() // 关闭
    } catch (e) {
      console.log(e)
    }
  }

  async handleClose() {
    try {
      // await this.hideCamera()
      await this.modalController.dismiss({text: this.scanText}, undefined, this.id || undefined)

      // const modal = await this.modalController.getTop();
      // modal.dismiss();
    } catch (e) {
      console.log(e)
    }
  }


}
