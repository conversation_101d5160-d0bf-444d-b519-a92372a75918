import {ApplicationRef, Component, OnInit} from '@angular/core'
import {Platform, AlertController, NavController, ModalController} from '@ionic/angular'
import {ActivatedRoute, Router} from '@angular/router'
import {UtilsService} from '@app/utils/utils.service'
import {TreeSelectPage} from '@app/components/tree-select/tree-select.page'
import {NativeStorage} from '@awesome-cordova-plugins/native-storage/ngx'
import {Camera, CameraOptions} from '@ionic-native/camera/ngx'
import {RelocationService} from '@services/sql/relocation.service'
import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';
import {EventService} from '@services/event/event.service'
import {AssetService} from '@services/api/asset.service'
import {LoadingService} from '@app/utils/loading.service'

@Component({
  selector: 'app-allocation-item-list',
  templateUrl: './allocation-item-list.page.html',
  styleUrls: ['./allocation-item-list.page.scss'],
})
export class AllocationItemListPage implements OnInit {

  public barcode = ''
  public data: any = {}
  public confirmation = true
  public locations: any = []
  public options: CameraOptions = {
    quality: 50,
    destinationType: this.camera.DestinationType.DATA_URL,
    encodingType: this.camera.EncodingType.JPEG,
    mediaType: this.camera.MediaType.PICTURE,
    // sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
  }
  constructor(
    private platform: Platform,
    private routeInfo: ActivatedRoute,
    private relocationService: RelocationService,
    public asset: AssetService,
    private router: Router,
    private loading: LoadingService,
    private eventService: EventService,
    private alertController: AlertController,
    private nav: NavController,
    private modalController: ModalController,
    private utils: UtilsService,
    private app: ApplicationRef,
    private navCtrl: NavController,
    private camera: Camera,
    private photoViewer: PhotoViewer,
  ) { }

  ngOnInit() {
    this.platform.ready().then(() => {
      // this.statusBar.backgroundColorByHexString('#003658')
      try {
        this.barcode = this.routeInfo.snapshot.queryParams.barcode
        const data = JSON.parse(this.routeInfo.snapshot.queryParams.data)
        data.change_num = 1
        data.old_department_name = data.department_name
        data.old_department_ename = data.department_ename
        data.department_name = ''
        data.department_ename = ''
        data.department_no = ''
        data.old_kind_name = data.kind_name
        data.old_kind_ename = data.kind_ename
        data.kind_name = ''
        data.kind_ename = ''
        data.kind_no = ''
        data.property_status_change_remark = ''
        this.data = data
        console.log(this.barcode, this.data)
      } catch (e) {
        console.error(e)
      }
      this.init()
      this.loadLocations()
    })
  }
  async init() {
    try {
      // const data = await this.allocationService.get(this.id)
      // console.log(data)
      // this.data = data
    } catch (e) {
      console.error(e)
    }
  }

  async loadLocations() {
    try {
      const res = await this.relocationService.getLocation()
      this.locations = res
    } catch (e) {
      console.error(e)
    }
  }
  getLocation(list, no) {
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.no === no) {
        return item
      }
      if (item.child && item.child.length > 0) {
        const child = this.getLocation(item.child, no)
        if (child) {
          return child
        }
      }
    }
    return null
  }
  getLocationName(no) {
    const item = this.getLocation(this.locations, no)
    return item ? item.location_name : ''
  }

  get title() {
    if (this.data && this.data.asset_check_no) {
      return 'Note No:' + this.data.asset_check_no
    }
    return ''
  }

  get onLine() {
    return navigator.onLine
  }
  onBackToHome() {
    // this.nav.pop()
    // this.nav.pop().then(() => this.nav.pop());
    this.nav.navigateBack('/function');
  }
  onBack() {
    this.eventService.emit('allocation-clear')
    this.nav.pop()
  }
  onSave() {
    this.saveData()
  }

  async saveData() {
    try {

    } catch (e) {
      console.error(e)
    }
    const property_location_serial_no = this.data.property_location_serial_no
    const property_barcode = this.data.property_barcode
    const property_location = this.data.property_location
    const change_num = this.data.change_num
    const department_no = this.data.department_no
    const department_name = this.data.department_name
    const kind_no = this.data.kind_no
    const kind_name = this.data.kind_name
    const property_status_change_remark = this.data.property_status_change_remark

    if (!change_num || change_num < 0) {
      this.utils.showToast('Please Fill Qty.')
      return
    }
    if (!department_no) {
      this.utils.showToast('請選擇新部門')
      return
    }
    if (!kind_no) {
      this.utils.showToast('請選擇新物品分類')
      return
    }


    const post_data = {}
    post_data['post_data[0][property_location_serial_no]'] = property_location_serial_no
    post_data['post_data[0][property_barcode]'] = property_barcode
    post_data['post_data[0][property_location]'] = property_location
    post_data['post_data[0][change_num]'] = change_num
    post_data['post_data[0][department_no]'] = department_no
    post_data['post_data[0][department_name]'] = department_name
    post_data['post_data[0][kind_no]'] = kind_no
    post_data['post_data[0][kind_name]'] = kind_name
    post_data['post_data[0][property_status_change_remark]'] = property_status_change_remark
    debugger
    this.asset.createPropertyAllocation(post_data).then(async (res: any) => {
      console.log(res)
      debugger
      if (res && res.status === 200) {
        // await this.utils.showMsg('提交成功！')
        const alert = await this.alertController.create({
          header: 'Tips',
          message: 'Upload successful!',
          backdropDismiss: false,
          buttons: [
            {
              text: 'Back',
              role: 'cancel',
              cssClass: 'secondary',
              handler: () => {
                this.onBackToHome()
              }
            }, {
              text: 'Next',
              handler: (data) => {
                this.onBack()
              }
            }
          ]
        });
        await alert.present();
        await alert.onDidDismiss()
      } else {
        console.error('失敗')
        return Promise.reject(res)
      }
      // this.eventService.emit('update_allocation_list')
      // this.nav.pop()
    }).catch((err: any) => {
      console.error(err)
      if (err) {
        if (err.msg && typeof err.msg === 'string') {
          this.utils.showMsg(err.msg)
          return
        }
      }
      this.utils.showMsg('Submit failed')
    })
  }
  getLocationArr(list, codes) {
    const arr = []
    for (const item of list) {
      if (codes.includes(item.no)) {
        arr.push(item)
      }
      if (Array.isArray(item.child) && item.child.length > 0) {
        const child = this.getLocationArr(item.child, codes)
        if (child.length > 0) {
          arr.push(...child)
        }
      }
    }
    return arr
  }

  // 選擇部門
  async onSelectDepartment() {
    let list: unknown = []
    try {
      list = await this.asset.getAppDepartments()
      // if (Array.isArray(this.data.location_list) && this.data.location_list.length > 0) {
      //   list = this.getLocationArr(list, this.data.location_list)
      // }
    } catch (e) {
      console.error(e)
    }
    const res: any = await this.utils.showModal(TreeSelectPage, {
      list,
      title: '選擇新部門',
      codeKey: 'no',
      nameKey: 'department_name',
    })
    console.log(res)
    if (res && res.data && res.data.save) {
      const data = res.data.data
      this.data.department_no = data.no
      this.data.department_name = data.department_name
      this.data.department_ename = data.department_ename
      this.app.tick()
    }
  }
  // 選擇部門
  async onSelectKind() {
    let list: unknown = []
    try {
      list = await this.asset.getAppKinds()
      // if (Array.isArray(this.data.location_list) && this.data.location_list.length > 0) {
      //   list = this.getLocationArr(list, this.data.location_list)
      // }
    } catch (e) {
      console.error(e)
    }
    const res: any = await this.utils.showModal(TreeSelectPage, {
      list,
      title: '選擇新物品分類',
      codeKey: 'no',
      nameKey: 'kind_name',
    })
    console.log(res)
    if (res && res.data && res.data.save) {
      const data = res.data.data
      this.data.kind_no = data.no
      this.data.kind_name = data.kind_name
      this.data.kind_ename = data.kind_ename
      this.app.tick()
    }
  }

  async searchItem() {
    if (!this.onLine) {
      this.utils.showToast('Please check WIFI connection')
      return
    }
    const codeno = this.data.property_barcode
    await this.loading.start()
    let data = null
    let itemurl = ''
    try {
      data = await this.asset.getItemUrl(codeno)
      itemurl = data.itemurl
      console.log(data)
    } catch (err) {
      console.error(err)
      this.utils.showMsg('Barcode No. not Found')
      await this.loading.end()
      return
    }
    if (itemurl) {
      this.openItemDetail(codeno, itemurl)

    }

    await this.loading.end()
  }

  openItemDetail(codeno, itemurl) {
    this.navCtrl.setDirection('forward');
    this.router.navigate(
      ['/item-detail/item'],
      {
        queryParams: {
          codeno,
          url: itemurl
        },
      }
    )
  }
}
