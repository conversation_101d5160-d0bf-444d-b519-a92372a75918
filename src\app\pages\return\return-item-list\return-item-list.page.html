<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onBackToHome()">Back</ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onSave()">Submit</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list lines="full">
    <ion-list-header lines="full">
      <ion-label>Article Information</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">Barcode No.</ion-label>
      <ion-text class="item-value">{{ data.property_barcode }}</ion-text>
    </ion-item>
    <ion-item lines="full" detail (click)="searchItem()">
      <ion-label position="fixed">Article Name</ion-label>
      <ion-text class="item-value">{{ data.property_name }}</ion-text>
      <ion-text slot="end" class="item-value placeholder">Detail</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Department</ion-label>
      <ion-text class="item-value">{{ data.old_department_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Category</ion-label>
      <ion-text class="item-value">{{ data.old_kind_name }}</ion-text>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Location</ion-label>
      <ion-text class="item-value">{{ data.location_ename }}</ion-text>
    </ion-item>
    <ion-item-divider></ion-item-divider>
    <ion-list-header lines="full">
      <ion-label>Return Info.</ion-label>
    </ion-list-header>
    <ion-item lines="full">
      <ion-label position="fixed">
        <ion-text color="danger">*</ion-text>Return To</ion-label>
      <div style="width: 100%">
        <ion-select
            [(ngModel)]="data.property_location_serial_status"
            class="item-value"
            placeholder="Select"
            okText="Confirm"
            cancelText="Cancel" style="width: 100%; max-width: 100%;">
          <ion-select-option value="1">Normal</ion-select-option>
          <ion-select-option value="2">Repair</ion-select-option>
          <ion-select-option value="3">Damage</ion-select-option>
          <ion-select-option value="4">Wait to WO</ion-select-option>
        </ion-select>
      </div>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed"><ion-text color="danger">*</ion-text>Return Qty.</ion-label>
      <ion-input [(ngModel)]="data.change_num" type="number" [min]="0" placeholder="Please Fill Qty."></ion-input>
    </ion-item>
    <ion-item lines="full">
      <ion-label position="fixed">Remark</ion-label>
      <ion-textarea [(ngModel)]="data.property_return_remark" class="remark-input" placeholder="Remark"></ion-textarea>
    </ion-item>
  </ion-list>
</ion-content>
