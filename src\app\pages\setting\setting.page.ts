import { Component, OnInit } from '@angular/core';
// import { UniqueDeviceID } from '@ionic-native/unique-device-id/ngx';
import {NavController, AlertController, Platform} from '@ionic/angular'
import { Device } from '@ionic-native/device/ngx';
import {AppCheckService} from '@services/api/app-check.service'
import {UtilsService} from '@app/utils/utils.service'
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import md5 from 'md5'
import {StorageService} from '@services/storage/storage.service'
import {TranslateService} from '@ngx-translate/core'
import {RelocationService} from '@services/sql/relocation.service'

@Component({
  selector: 'app-setting',
  templateUrl: './setting.page.html',
  styleUrls: ['./setting.page.scss'],
})
export class SettingPage implements OnInit {

  public cmpyUUID = ''
  public cmpyToken = ''
  public deviceID = ''
  public host = ''
  public realHost = ''
  public project = ''

  public stockTakeConfirmation = false

  public cmpyInit = false
  public settingInit = false

  public poweredBy = ''
  public hotline = ''

  public companyLoading = false
  public serverLoading = false

  constructor(
    private platform: Platform,
    // private uniqueDeviceID: UniqueDeviceID
    private device: Device,
    private navController: NavController,
    private appCheck: AppCheckService,
    public util: UtilsService,
    private nativeStorage: StorageNativeService,
    private storage: StorageService,
    private utils: UtilsService,
    public translate: TranslateService,
    public relocationService: RelocationService,
    private alertController: AlertController,
  ) {
    this.poweredBy = this.storage.appInfo.poweredBy
    this.hotline = this.storage.appInfo.hotline
  }

  ngOnInit() {
    this.platform.ready()
      .then(async () => {
        // 設備ID
        if ( this.platform.is('mobile') &&
          (this.platform.is('android') || this.platform.is('ios')) &&
          this.device && this.device.uuid
        ) {
          this.deviceID = this.device.uuid
          // this.init = true
        } else {
          this.deviceID = 'web-test-id-123456789'
        }
        this.nativeStorage.getItem('cmpyUUID').then(cmpyUUID => {
          this.cmpyUUID = cmpyUUID || ''
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('cmpyToken').then(cmpyToken => {
          this.cmpyToken = cmpyToken || ''
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('cmpyInit').then(cmpyInit => {
          this.cmpyInit = cmpyInit === '1'
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('host').then(host => {
          this.host = host || ''
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('realHost').then(realHost => {
          this.realHost = realHost || ''
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('project').then(project => {
          this.project = project || ''
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('stockTakeConfirmation').then(stockTakeConfirmation => {
          this.stockTakeConfirmation = stockTakeConfirmation === '1'
        }).catch(e => console.error(e))
        this.nativeStorage.getItem('settingInit').then(settingInit => {
          this.settingInit = settingInit === '1'
        }).catch(e => console.error(e))
      })
      .catch(err => {
        console.error(err)
      })

  }

  onBack() {
    this.navController.pop()
  }

  async showAlert(msg: string) {
    const alert = await this.alertController.create({
      // header: 'Tips',
      message: msg,
      buttons: [this.translate.instant('BUTTON.CONFIRM')],
    });
    await alert.present();
  }
  async onConfirmCmpyUUID() {
    if (this.companyLoading) { return }
    this.companyLoading = true
    const cmpyUUID = this.cmpyUUID
    const device = this.deviceID
    if (cmpyUUID.replace(/ /g, '').length === 0) {
      // this.util.showToast(this.translate.instant('SETTING.INPUT_COMPANY_ID'))
      this.showAlert(this.translate.instant('SETTING.INPUT_COMPANY_ID'))
      this.companyLoading = false
      return
    }
    try {
      const res: any = await this.appCheck.getEncCompanyUUID(cmpyUUID, device)
      const cmpyToken = res.token
      this.cmpyToken = cmpyToken
      this.cmpyInit = true
      this.nativeStorage.setItem('cmpyUUID', cmpyUUID)
      this.nativeStorage.setItem('cmpyToken', cmpyToken)
      this.nativeStorage.setItem('cmpyInit', '1')
    } catch (e) {
      console.error(e)
      if (e && e.msg) {
        // this.util.showToast(e.msg)
        this.showAlert(e.msg)
      } else {
        // this.util.showToast('Company Identifier invalid.')
        this.showAlert('Company Identifier invalid.')
      }
    }
    this.companyLoading = false
  }
  async onResetCmpyUUID() {
    if (!await this.utils.confirm(
      this.translate.instant('MESSAGE.WARNING'),
      this.translate.instant('SETTING.CONFIRM_RESET'))) {
      return
    }

    this.cmpyUUID = ''
    this.cmpyToken = ''
    this.cmpyInit = false
    this.host = ''
    this.realHost = ''
    this.project = ''
    this.settingInit = false
    this.nativeStorage.setItem('cmpyUUID', '')
    this.nativeStorage.setItem('cmpyToken', '')
    this.nativeStorage.setItem('cmpyInit', '0')
    this.nativeStorage.setItem('host', '')
    this.nativeStorage.setItem('realHost', '')
    this.nativeStorage.setItem('project', '')
    this.nativeStorage.setItem('settingInit', '0')
    this.nativeStorage.setItem('stockTakeConfirmation', '1')
  }

  async onCheckSetting() {
    if (this.serverLoading) { return }
    this.serverLoading = true
    const host = this.host
    const realHost = this.utils.convertRealHost(host)
    const project = this.project
    // 獲取Asset的公司Code
    let cmpyCode = ''
    try {
      const res: any = await this.appCheck.getcmpyCode(realHost, project)
      console.log(res)
      cmpyCode = res.replace(/"/g, '')
    } catch (e) {
      if (e && e.msg) {
        this.showAlert(e.msg)
      } else {
        this.showAlert('can not connect to the server')
      }
      console.error(e)
      this.serverLoading = false
      return
    }
    // 驗證Token
    const val = `${this.cmpyUUID}-${this.deviceID}-${cmpyCode}`
    const md5Str = md5(md5(val))
    if (md5Str !== this.cmpyToken) {
      this.showAlert('Company Indentifier incorrect')
      this.serverLoading = false
      return
    }
    // 訪問Norray中心綁定設備uuid
    try {
      const res = await this.appCheck.confirmCompanyUUID(this.cmpyUUID, this.deviceID)
      console.log(res)
    } catch (e) {
      console.error(e)
      // this.util.showToast(this.translate.instant('SETTING.DEVICE_BINDING_FAILED'))
      this.showAlert(this.translate.instant('SETTING.DEVICE_BINDING_FAILED'))
      this.serverLoading = false
      return
    }

    // 檢查是否連接Asset Server成功
    try {
      const res = await this.appCheck.checksetting(realHost, project)
      console.log(res)
      this.nativeStorage.setItem('host', host)
      this.nativeStorage.setItem('realHost', realHost)
      this.nativeStorage.setItem('project', project)
      this.nativeStorage.setItem('settingInit', '1')
      this.nativeStorage.setItem('stockTakeConfirmation', this.stockTakeConfirmation ? '1' : '0')
      this.util.showToast(this.translate.instant('SETTING.CONNECTION_SUCCESS'))
      this.navController.pop()
    } catch (e) {
      console.error(e)
      this.showAlert(this.translate.instant('SETTING.CONNECTION_FAILED'))
    }
    this.serverLoading = false
  }

}
