import * as dateUtils from './dateUtils'

export function sleep(time) {
  return new Promise(resolve => setTimeout(resolve, time))
}

export function itemRemove(selector: string) {
  return new Promise<void>(resolve => {
    // const el = (window.document.querySelector(selector) as HTMLElement)
    // el.classList.remove('item-enter')
    // el.classList.add('item-leave')
    // el.classList.remove('item-enter')
    // el.classList.add('item-leave')
    const els = window.document.querySelectorAll(selector)
    els.forEach((el: HTMLElement) => {
      el.classList.remove('item-enter')
      el.classList.add('item-leave')
    })
    if (els.length) {
      sleep(300).then(() => {
        resolve()
      })
    } else {
      resolve()
    }
  })
}


export function isDateType(d) {
  return Object.prototype.toString.call(d) !== '[object Date]' || isNaN(d.getTime())
}

export function dateFormat(date, format = 'yyyy-MM-dd') {
  if (isDateType(date)) {
    return dateUtils.format(date, format)
  } else {
    const d = new Date(date)
    return dateUtils.format(d, format)
  }
}

export function getWeekFirst(date) {
  let d = new Date()
  if (isDateType(date)) {
    d = date
  } else {
    d = new Date(date)
  }
  d.setDate(-d.getDay() + 1)
  return d
}

export function getWeekLast(date) {
  let d = new Date()
  if (isDateType(date)) {
    d = date
  } else {
    d = new Date(date)
  }
  d.setDate(d.getDate() + 6 - d.getDay())
  return d
}
