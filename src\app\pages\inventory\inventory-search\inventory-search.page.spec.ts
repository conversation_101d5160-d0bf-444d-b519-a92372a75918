import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InventorySearchPage } from './inventory-search.page';

describe('InventorySearchPage', () => {
  let component: InventorySearchPage;
  let fixture: ComponentFixture<InventorySearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InventorySearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InventorySearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
