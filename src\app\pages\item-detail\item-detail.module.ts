import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { ItemDetailPageRoutingModule } from './item-detail.router.module'


import { ItemDetailPage } from './item-detail.page';
import {TranslateModule} from '@ngx-translate/core'

// const routes: Routes = [
//   {
//     path: '',
//     component: ItemDetailPage
//   }
// ];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
    ItemDetailPageRoutingModule,
    TranslateModule,
  ],
  declarations: [ItemDetailPage]
})
export class ItemDetailPageModule {}
