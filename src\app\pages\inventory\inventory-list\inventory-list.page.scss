.info-box {
  padding: 0.5rem 0;
  width: 100%;
  z-index: 11;
  span.info-item {
    display: block;
    text-overflow: ellipsis;
    font-size: 0.7rem;
    &.no {
      font-size: 1rem;
    }
  }

}
ion-list {
  ion-item-sliding {
    ion-item {
      padding: 0;
      --padding-bottom: 0;
      --padding-top: 0;
    }
  }
}



.header-btn {
  background: #519BD4;
  color: #FFFFFF;
  --ion-toolbar-color: #FFF;
  border-radius: 5px;
}

.header-row {
  //padding-right: 20px;
  //--padding-bottom: 30px;
  --background: #CBCBCB;
  background: #CBCBCB;
  vertical-align: middle;
  .label {
    text-align: right;
    width: 100%;
  }
  ::ng-deep {
    ion-checkbox {
      margin-right: 20px;
    }
    ion-item {
      padding: 0.5rem 0;
      --padding-bottom: 0.5rem;
      --padding-top: 0.5rem;
    }
  }
}
