import {NgModule} from '@angular/core'
import {BrowserModule} from '@angular/platform-browser'
import {RouteReuseStrategy} from '@angular/router'
import { FormsModule } from '@angular/forms';
import {IonicModule, IonicRouteStrategy, AnimationController} from '@ionic/angular'
import {SplashScreen} from '@ionic-native/splash-screen/ngx'
// 系統狀態欄
import {StatusBar} from '@ionic-native/status-bar/ngx'
import {AppRoutingModule} from './app-routing.module'
import {AppComponent} from './app.component'
// 掃碼
import {QRScanner} from '@ionic-native/qr-scanner/ngx'
import {ScannerPagePage} from './pages/scanner-page/scanner-page.page'
// 屏幕亮度
import { Brightness } from '@ionic-native/brightness/ngx';
// 振动
import { Vibration } from '@ionic-native/vibration/ngx';

import {SqlService} from './services/sql/sql.service'
import {StorageService} from './services/storage/storage.service'
import {RequestService} from './services/request/request.service'
import {StoreService} from './services/store/store.service'
import {AuthService} from './services/auth/auth.service'
import {LoginService} from './services/api/login.service'
import {StorageNativeService} from './services/storageNative/storage-native.service'
import {RelocationService} from './services/sql/relocation.service'

import {HTTP} from '@ionic-native/http/ngx'
import {IsDebug} from '@ionic-native/is-debug/ngx';
import { enterAnimation, leaveAnimation, fancyAnimation} from './animations/rightModal'
import {Storage} from '@ionic/storage-angular';
import { AppVersion } from '@ionic-native/app-version/ngx';

import { iosTransitionAnimation } from '@ionic/core/dist/collection/utils/transition/ios.transition.js';
// 動畫
// 系統導航欄
// import { NavigationBarColor } from 'ionic-plugin-navigation-bar-color';
// import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
// import { LyThemeModule, LY_THEME } from '@alyle/ui';
// import { MinimaLight } from '@alyle/ui/themes/minima';

// import { iosTransitionAnimation } from '@ionic/core/dist/collection/utils/transition/ios.transition.js';
// 設備
import { Device } from '@ionic-native/device/ngx';
// 瀏覽器
// import { BrowserTab } from '@ionic-native/browser-tab/ngx';
import { ThemeableBrowser, ThemeableBrowserOptions, ThemeableBrowserObject } from '@ionic-native/themeable-browser/ngx';
// 拍照
import { Camera, CameraOptions } from '@ionic-native/camera/ngx';
// 圖片預覽
import { PhotoViewer } from '@ionic-native/photo-viewer/ngx';

// 數據庫
import { SQLite, SQLiteObject } from '@awesome-cordova-plugins/sqlite/ngx'
// android全面屏
import {AndroidNotch} from '@awesome-cordova-plugins/android-notch/ngx';
// 國際化
import {TranslateModule, TranslateLoader} from '@ngx-translate/core';
import {TranslateHttpLoader} from '@ngx-translate/http-loader';
import {HttpClient, HttpClientModule} from '@angular/common/http';
export function HttpLoaderFactory(httpClient: HttpClient) {
  return new TranslateHttpLoader(httpClient);
}
import { SelectLocationPage } from '@pages/inventory/select-location/select-location.page'
import { InventorySearchPage } from '@pages/inventory/inventory-search/inventory-search.page'
import { CancellationSearchPage } from '@pages/cancellation/cancellation-search/cancellation-search.page'
import { RelocationSearchPage } from '@pages/relocation/relocation-search/relocation-search.page'
import { TreeSelectPage } from '@app/components/tree-select/tree-select.page'
import {CardModule} from '@app/components/card/card.module'
import { InventoryScannerPage } from '@pages/inventory/inventory-scanner/inventory-scanner.page'
// 音頻
import { NativeAudio } from '@ionic-native/native-audio/ngx';
// 網絡
import { Network } from '@ionic-native/network/ngx';
import { Animation } from '@ionic/core';

// @ts-ignore
@NgModule({
  declarations: [
    AppComponent,
    ScannerPagePage,
    InventoryScannerPage,
    SelectLocationPage,
    InventorySearchPage,
    CancellationSearchPage,
    RelocationSearchPage,
    TreeSelectPage,
  ],
  entryComponents: [
    ScannerPagePage,
    InventoryScannerPage,
    SelectLocationPage,
    InventorySearchPage,
    CancellationSearchPage,
    RelocationSearchPage,
    TreeSelectPage,
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    FormsModule,
    IonicModule.forRoot({
      backButtonText: '',
      mode: 'ios',
      // navAnimation: fancyAnimation,
      navAnimation: fancyAnimation,
      // tslint:disable-next-line:only-arrow-functions
      // navAnimation(baseEl: any, position?: any): Animation  {
      //   const baseAnimation = new AnimationController().create();
      //   const hostEl = (baseEl.host || baseEl) as HTMLElement;

      //   const wrapperAnimation = new AnimationController().create();
      //   const wrapperAnimation2 = new AnimationController().create();
      //   if (position.direction === 'forward') {
      //     wrapperAnimation.addElement(position.enteringEl);
      //     wrapperAnimation.fromTo('transform', `translateX(100%)`, 'translateX(0px)');
      //     wrapperAnimation.fromTo('opacity', 0.3, 1);

      //     wrapperAnimation2.addElement(position.leavingEl);
      //     wrapperAnimation2.fromTo('transform', `translateX(0)`, 'translateX(-50%)');
      //     wrapperAnimation2.fromTo('opacity', 1, 0.5);
      //   }

      //   if (position.direction === 'back') {
      //     wrapperAnimation.addElement(position.leavingEl);
      //     wrapperAnimation.fromTo('transform', `translateX(0)`, 'translateX(100%)');
      //     wrapperAnimation.fromTo('opacity', 1, 0);

      //     wrapperAnimation2.addElement(position.enteringEl);
      //     wrapperAnimation2.fromTo('transform', `translateX(-90%)`, 'translateX(0)');
      //     wrapperAnimation2.fromTo('opacity', 0.5, 1);
      //   }
      //   return baseAnimation
      //     .addElement(hostEl)
      //     .easing('cubic-bezier(.36,.66,.04,1)')
      //     .duration(600)
      //     .addAnimation(wrapperAnimation)
      //     .addAnimation(wrapperAnimation2);
      // }
      // navAnimation: iosTransitionAnimation,
      //   navAnimation: (
      //     AnimationC: Animation,
      //     baseEl: any,
      //     position?: any): Promise<Animation> => {
      //     const baseAnimation = new AnimationC();
      //     const hostEl = (baseEl.host || baseEl) as HTMLElement;

      //     const wrapperAnimation = new AnimationC();
      //     const wrapperAnimation2 = new AnimationC();
      //     if (position.direction === 'forward') {
      //       wrapperAnimation.addElement(position.enteringEl);
      //       wrapperAnimation.fromTo('transform', `translateX(100%)`, 'translateX(0px)');
      //       wrapperAnimation.fromTo('opacity', 0.3, 1);

      //       wrapperAnimation2.addElement(position.leavingEl);
      //       wrapperAnimation2.fromTo('transform', `translateX(0)`, 'translateX(-50%)');
      //       wrapperAnimation2.fromTo('opacity', 1, 0.5);
      //     }

      //     if (position.direction === 'back') {
      //       wrapperAnimation.addElement(position.leavingEl);
      //       wrapperAnimation.fromTo('transform', `translateX(0)`, 'translateX(100%)');
      //       wrapperAnimation.fromTo('opacity', 1, 0);

      //       wrapperAnimation2.addElement(position.enteringEl);
      //       wrapperAnimation2.fromTo('transform', `translateX(-90%)`, 'translateX(0)');
      //       wrapperAnimation2.fromTo('opacity', 0.5, 1);
      //     }
      //     return Promise.resolve(baseAnimation
      //       .addElement(hostEl)
      //       .easing('cubic-bezier(.36,.66,.04,1)')
      //       .duration(600)
      //       .add(wrapperAnimation)
      //       .add(wrapperAnimation2));
      //   }
    }),
    AppRoutingModule,
    CardModule,
    // BrowserAnimationsModule, // 動畫
    // LyThemeModule.setTheme('minima-light')
  ],
  providers: [
    StatusBar,
    // NavigationBarColor,
    SplashScreen,

    IsDebug,
    SQLite,
    HTTP,
    QRScanner, // 掃碼
    Brightness, // 屏幕亮度
    Camera, // 拍照
    PhotoViewer, // 圖片
    Vibration, // 振动
    NativeAudio, // 音頻
    Device,
    Network,
    AndroidNotch, // 屏幕
    // BrowserTab,
    ThemeableBrowser,

    StorageService,
    RequestService,
    SqlService,
    StoreService,
    AuthService,
    LoginService,
    Storage,
    StorageNativeService,
    AppVersion,
    // RelocationService,

    {provide: RouteReuseStrategy, useClass: IonicRouteStrategy},
    // { provide: LY_THEME, useClass: MinimaLight, multi: true }
  ],
  bootstrap:
    [AppComponent],
  exports: [
  ]
})

export class AppModule {
}
