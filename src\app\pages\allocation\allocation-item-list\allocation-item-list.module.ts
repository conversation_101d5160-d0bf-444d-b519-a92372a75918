import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { AllocationItemListPage } from './allocation-item-list.page';

const routes: Routes = [
  {
    path: '',
    component: AllocationItemListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [AllocationItemListPage],
  // entryComponents: []
})
export class AllocationItemListPageModule {}
