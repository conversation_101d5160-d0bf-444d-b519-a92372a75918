import {Injectable, Input, NgModule} from '@angular/core';
import {rightEnterAnimation, rightLeaveAnimation, enterAnimation, leaveAnimation} from '../animations/rightModal'
import {ScannerPagePage} from '../pages/scanner-page/scanner-page.page'
import {ModalController} from '@ionic/angular'

@Injectable({
  providedIn: 'root',
})
export class ScannerService {

  constructor(
    private modalController: ModalController,
  ) {
  }

  async showScanModal(props: {
    title: string,
    tips: string,
    page: any,
    validate?: any,
    id?: string
    showConfirm?: boolean
  }): Promise<string> {
    console.log('showScanModal')
    const modal = await this.modalController.create({
      // mode: 'md',
      enterAnimation: enterAnimation,
      leaveAnimation: leaveAnimation,
      component: ScannerPagePage,
      componentProps: {
        ...props,
        id: 'scanner-modal',
      },
      id: 'scanner-modal',
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    return res.data && res.data.text || ''
  }
}
