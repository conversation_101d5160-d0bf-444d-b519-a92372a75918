
<div class="ion-page" id="main-page">
<ion-header>
  <ion-toolbar>
    <ion-buttons slot="end">
      <ion-button class="header-btn" (click)="onAdd()">Add</ion-button>
    </ion-buttons>
    <ion-title>Article Relocation</ion-title>
    <ion-buttons slot="start">
      <ion-button class="header-btn" (click)="onBack()">Back</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<!--<ion-tab-bar slot="top">-->
<!--  &lt;!&ndash;<ion-button color="asset" (click)="onUpload()">Upload Data</ion-button>&ndash;&gt;-->
<!--  <ion-list style="width: 100%">-->
<!--    <ion-item class="header-row" lines="full" [button]="false">-->
<!--      <span class="label">All Select</span>-->
<!--      <ion-checkbox [(ngModel)]="allCheck" slot="end" (ngModelChange)="onCheckAll()"></ion-checkbox>-->
<!--    </ion-item>-->
<!--  </ion-list>-->
<!--</ion-tab-bar>-->
<ion-content>
  <!--<ion-button (click)="test()">texs</ion-button>-->
  <ion-list>
    <!--<ion-item class="header-row" lines="full">-->
    <!--  <span class="label">All Select</span>-->
    <!--  <ion-checkbox [(ngModel)]="allCheck" slot="end"></ion-checkbox>-->
    <!--</ion-item>-->
    <ion-item-sliding *ngFor="let item of list">
      <ion-item button detail lines="full">
        <div class="info-box" (click)="onClick(item);" tappable>
          <span class="info-item no">Bar Code No.: {{ item.property_barcode }}</span>
          <span class="info-item">Name: {{ item.property_name }}</span>
          <span class="info-item">Location: {{ item.location_name }}</span>
          <span class="info-item">New Location: {{ getLocationName(item.relocation_location_no) }}</span>
          <span class="info-item">Status: <ion-text [color]="getStatusColor(item)">{{ getStatus(item) }}</ion-text></span>
        </div>

        <!--<ion-checkbox [(ngModel)]="item.check" slot="end" (ngModelChange)="onChangeItem(item)"></ion-checkbox>-->

      </ion-item>
      <ion-item-options side="end">
        <ion-item-option color="danger" (click)="onDelete(item)">Delete</ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>
  <app-empty *ngIf="list.length === 0" [message]="'Please add new article relocation.'" mode="dark"></app-empty>
</ion-content>
<ion-toolbar slot="bottom">
  <ion-button color="asset" (click)="onUpload()" style="margin: 0 auto 0 0.5rem;">Upload Data</ion-button>
</ion-toolbar>
<!--<ion-tabs>-->
<!--</ion-tabs>-->

</div>
