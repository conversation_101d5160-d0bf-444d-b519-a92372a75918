import {Component, OnInit} from '@angular/core'
import {UtilsService} from '@app/utils/utils.service'
import {AssetService} from '@services/api/asset.service'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {Modal<PERSON>ontroller, NavController} from '@ionic/angular'
import {CancellationSearchPage} from '@pages/cancellation/cancellation-search/cancellation-search.page'
import {Router} from '@angular/router'
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import {CancellationService} from '@services/sql/cancellation.service'
import { ApplicationRef } from '@angular/core';
import { Camera, CameraOptions } from '@ionic-native/camera/ngx';
import {PhotoViewer} from '@ionic-native/photo-viewer/ngx'
import {EventService} from '@services/event/event.service'
import {LoadingService} from '@app/utils/loading.service'
import {RelocationService} from '@services/sql/relocation.service'

@Component({
  selector: 'app-cancellation-list',
  templateUrl: './cancellation-list.page.html',
  styleUrls: ['./cancellation-list.page.scss'],
})
export class CancellationListPage implements OnInit {
  public options: CameraOptions = {
    quality: 50,
    destinationType: this.camera.DestinationType.DATA_URL,
    encodingType: this.camera.EncodingType.JPEG,
    mediaType: this.camera.MediaType.PICTURE,
    // sourceType: this.camera.PictureSourceType.SAVEDPHOTOALBUM,
  }
  public locations: any = []
  public list: any = []
  public allCheck = false
  public isInit = false
  constructor(
    private utils: UtilsService,
    private asset: AssetService,
    private modalController: ModalController,
    private nav: NavController,
    private cancellationService: CancellationService,
    private relocationService: RelocationService,
    private eventService: EventService,
    private router: Router,
    private nativeStorage: StorageNativeService,
    private app: ApplicationRef,
    private loading: LoadingService,
    private camera: Camera,
    private photoViewer: PhotoViewer
  ) { }

  async ngOnInit() {
    // this.hide()
    // await this.cancellationService.clear()
    await this.loadData()
    this.loadLocations()
    // this.onAdd()
    this.eventService.event.on('update_cancellation_list', () => {
      this.loadData()
      this.loadLocations()
    })
  }
  onBack() {
    this.nav.pop()
  }
  hidePage() {
    // @ts-ignore
    document.getElementsByTagName('app-cancellation-list')[0].style.display = 'none'
  }
  showPage() {
    setTimeout(() => {
      // @ts-ignore
      document.getElementsByTagName('app-cancellation-list')[0].style.display = ''
    }, 1500)
  }
  get onLine() {
    return navigator.onLine
  }
  async loadData() {
    await this.loading.start()
    this.list = []
    this.cancellationService.all().then(res => {
      console.log(res)
      this.list = res
      this.app.tick();
    }).catch(err => {
      console.error(err)
    }).finally(() => {
      this.loading.end()
    })
  }
  async onAdd() {
    const data: any = await this.showSearchModal({})
    if (data && data.text) {
      this.isInit = true
      let isExist = false
      let insertId: any = ''
      if (data.propertyno) {
        // this.list.push(data)
        try {
          // const item = await this.asset.getAppCancellationInfo(data.text)
          // if (item) {
            isExist = true
          // }
        } catch (e) {
          console.error(e)
        }
        this.showPage()
        // if (isExist) {
        //   return
        // }
        try {
          insertId = await this.cancellationService.add({
            property_barcode: data.text,
            property_location_serial_no: '',
            property_location: '',
            change_num: 1,
            img: '',
            property_status_change_woreason: '0',
            property_status_change_remark: '',
            property_no: data.propertyno,
            property_ename: data.propertyname,
            property_name: data.propertyname,
            property_status: '',
            location_list: [],
            ...data
          })
        } catch (e) {
          console.error(e)
        }
      } else {
        try {
          insertId = await this.cancellationService.add({
            property_barcode: data.text,
            property_location_serial_no: '',
            property_location: '',
            change_num: 1,
            img: '',
            property_status_change_woreason: '0',
            property_status_change_remark: '',
            property_no: '',
            property_ename: '',
            property_name: '',
            property_status: '',
            location_list: [],
          })
        } catch (e) {
          console.error(e)
        }
      }
      if (insertId) {
        this.openItem(insertId)
      }
    } else {
      // if (!this.isInit) {
      //   this.onBack()
      // } else {
        this.showPage()
      // }
    }
    if (this.isInit) {
      this.showPage()
      await this.loadData()
    }
  }


  async checkNo(checkno) {
    try {
      const res = await this.asset.checkListNo(checkno)
      return true
    } catch (e) {
      console.error(e)
    }
    return false
  }


  async showSearchModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: CancellationSearchPage,
      componentProps: {
        ...props,
        id: 'cancellation-search-modal'
      },
      id: 'cancellation-search-modal'
    })
    await modal.present()
    const res: any = await modal.onDidDismiss()
    if (res.data && res.data.save) {
      if (res.data.info) {
        return {
          ...res.data.info,
          list: res.data.list,
          text: res.data.text
        }
      } else {
        return {
          text: res.data.text
        }
      }
    }
    return {}
  }

  onDelete(item) {
    console.log(item)
    this.cancellationService.delete(item.id)
    // const i = this.list.findIndex(i => i.id === item.id )
    this.loadData()
  }
  onClick(item) {
    console.log(item)
    this.openItem(item.id)
  }
  openItem(id) {
    this.nav.setDirection('forward');
    this.router.navigate(
      ['cancellation-item-list', id],
    )
  }

  async onUpload() {
    if (!await this.utils.confirm('Warning', 'Confirm the upload?', 'CONFIRM', 'CANCEL')) {
      return
    }
    if (!this.onLine) {
      this.utils.showMsg('Please check WIFI connection')
    }
    const list = this.list // .filter(i => i.check)
    if (list.length > 0) {
      console.log(list)
      let user_id = ''
      try {
        user_id = await this.nativeStorage.getItem('user_id')
      } catch (e) {
        console.log('無user_id')
        this.utils.showMsg('Timeout, Please re-login')
        return
      }
      await this.loading.start()
      const data = list.map(item => {
        return {
          id: item.id,
          property_location_serial_no: item.property_location_serial_no,
          property_barcode: item.property_barcode,
          property_location: item.property_location,
          change_num: item.change_num,
          img: item.img,
          property_status_change_woreason: item.property_status_change_woreason,
          property_status_change_remark: item.property_status_change_remark,
        }
      })
      // const post_data = new FormData()
      const post_data = {}
      for (let i = 0; i < data.length; i++) {
        const item = data[i]
        post_data['post_data[' + i + '][id]'] = item.id
        post_data['post_data[' + i + '][property_location_serial_no]'] = item.property_location_serial_no
        post_data['post_data[' + i + '][property_barcode]'] = item.property_barcode
        post_data['post_data[' + i + '][property_location]'] = item.property_location
        post_data['post_data[' + i + '][change_num]'] = item.change_num
        post_data['post_data[' + i + '][img]'] = item.img
        post_data['post_data[' + i + '][property_status_change_woreason]'] = item.property_status_change_woreason
        post_data['post_data[' + i + '][property_status_change_remark]'] = item.property_status_change_remark || ''
      }
      try {
        const res: any = await this.asset.checkCancellation(post_data)
        console.log(res)

        const result: any = res.data
          // tslint:disable-next-line:prefer-for-of
        for (let i = 0; i < result.length; i++) {
          const item = result[i]
          this.cancellationService.update({
            id: item.id,
            property_no: item.property_no,
            property_ename: item.property_ename,
            property_name: item.property_name,
            property_status: item.property_status,
            location_list: item.location_list,
          })
        }
        const isSuccess = res.data.every(i => i.property_status === 0)
        if (isSuccess) {
          const res2 = await this.asset.createCancellation(post_data)
          for (const item of list) {
            await this.cancellationService.delete(item.id)
          }
          this.utils.showToast('Upload complete!')
        } else {
          this.utils.showToast('Upload failed!')
        }
      } catch (e) {
        console.error(e)
        this.utils.showToast('Upload failed!')
      }
      this.loading.end()
      this.loadData()
    }
  }
  onCheckAll() {

    // const check = this.list.event(i => i.check)
    this.list.forEach(i => {
      i.check = this.allCheck
    })
  }
  onChangeItem(item) {
    this.allCheck = this.list.every(i => i.check)
  }

  addImg(item) {
    this.camera.getPicture(this.options).then((imageData) => {
      // imageData is either a base64 encoded string or a file URI
      // If it's base64 (DATA_URL):
      const base64Image = 'data:image/jpeg;base64,' + imageData;
      console.log(base64Image)
      this.cancellationService.update({
        id: item.id,
        img: base64Image
      })
      this.loadData()
    }, (err) => {
      // Handle error
      console.error(err)
    })
  }
  imgViewer(event, item) {
    event.stopPropagation()
    event.preventDefault()
    if (item.img) {
      this.photoViewer.show(item.img)
    }
  }

  getStatus(item) {
    switch (item.property_status) {
      case 0:
        return 'Normal'
      case 1:
        return 'Please Choose Location'
      case 2:
        if (item.location_list && item.location_list.length > 0) {
          return 'Invalid Barcode No. in Location'
        } else {
          return 'Barcode No. not Found'
        }
      case 3:
        return 'Invalid Qty.'
      default:
        return 'Not Approval'
    }
  }
  getStatusColor(item) {
    switch (item.property_status) {
      case 0:
        return 'success'
      case 1:
        return 'warning'
      case 2:
        return 'danger'
      case 3:
        return 'warning'
      default:
        return ''
    }
  }
  getLocationName(no) {
    const item = this.relocationService.getLocationItem(this.locations, no)
    return item ? item.location_name : ''
  }
  async loadLocations() {
    try {
      const res = await this.relocationService.getLocation()
      this.locations = res
    } catch (e) {
      console.error(e)
    }
  }
}
