import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CancellationItemListPage } from './cancellation-item-list.page';

describe('CancellationItemListPage', () => {
  let component: CancellationItemListPage;
  let fixture: ComponentFixture<CancellationItemListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CancellationItemListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CancellationItemListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
