import {Component, OnInit} from '@angular/core'
import {rightEnterAnimation, rightLeaveAnimation} from '@app/animations/rightModal'
import {ScannerPagePage} from '@pages/scanner-page/scanner-page.page'
import {ModalController, NavController} from '@ionic/angular'
import {AssetService} from '@services/api/asset.service'
import {UtilsService} from '@app/utils/utils.service'
import {LoadingService} from '@app/utils/loading.service'

@Component({
  selector: 'app-inventory-search',
  templateUrl: './inventory-search.page.html',
  styleUrls: ['./inventory-search.page.scss'],
})
export class InventorySearchPage implements OnInit {

  public noteNo = ''
  constructor(
    private modalController: ModalController,
    private asset: AssetService,
    private utils: UtilsService,
    private nav: NavController,
    private loading: LoadingService,
  ) { }

  ngOnInit() {
  }
  onBack() {
    this.modalController.dismiss({
      cancel: true
    })
  }
  async onScan() {
    const text = await this.showScanModal({})
    if (text) {
      this.noteNo = text
      this.handleSearch(text)
    }
  }
  async onConfirm() {
    const text = this.noteNo
    this.handleSearch(text)
  }



  async showScanModal(props: any) {
    const modal = await this.modalController.create({
      // enterAnimation: rightEnterAnimation,
      // leaveAnimation: rightLeaveAnimation,
      component: ScannerPagePage,
      componentProps: {
        ...props,
        id: 'list-scanner-modal'
      },
      id: 'list-scanner-modal'
    })
    await modal.present()
    const res = await modal.onDidDismiss()
    return res.data && res.data.text || ''
  }


  async checkNo(checkno) {
    try {
      return await this.asset.checkListNo(checkno)
    } catch (e) {
      console.error(e)
    }
    return false
  }
  async handleSearch(text) {
    if (typeof text !== 'string' || text.length <= 0) {
      return
    }
    await this.loading.start()
    const data = await this.checkNo(text)
    let list: any = []
    if (data) {
      list = await this.getNoteList(text)

      await this.loading.end()
      this.modalController.dismiss({
        save: true,
        info: data,
        list
      })
    } else {
      await this.loading.end()
      this.utils.showMsg('Enquiry Register No. not Found')
    }
  }
  async getNoteList(checkno) {
    try {
      return await this.asset.getItemList(checkno)
    } catch (e) {
      console.error(e)
    }
    return false
  }
}
