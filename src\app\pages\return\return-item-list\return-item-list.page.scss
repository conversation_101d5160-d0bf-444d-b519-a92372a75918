.item-header {
  ion-row {
    width: 100%;
    ion-col {
      justify-content: center;
      justify-items: center;
      display: flex;
      align-items: center;
    }
  }
}

.item-data {
  font-size: 13px;
  ion-row {
    width: 100%;
    ion-col {
      span {
        display: block;
        ion-icon {
          font-size: inherit;
          line-height: inherit;
          vertical-align: middle;
        }
        &.location {
          color: #5891c0;
        }
      }
      &:not(.info) {
        justify-content: center;
        justify-items: center;
        display: flex;
        align-items: center;
      }
      &.stq {
        color: #bcca7a;
      }
      &.aw {
        color: #b5313a;
      }

      ion-button {
        font-size: 9px;
        max-width: 100%;
        font-size: 0.4rem;
        min-height: 25px;
      }
    }
  }
}


.header-btn {
  background: #519BD4;
  color: #FFFFFF;
  --ion-toolbar-color: #FFF;
  border-radius: 5px;
}

.remark-input {
  ::ng-deep {
    textarea {
      height: 150px;
      padding-inline-start: var(--padding-end);
    }
  }
}

.item-value {
  --padding-top: 10px;
  --padding-end: 8px;
  --padding-bottom: 10px;
  --padding-start: 8px;
  padding-inline-start: var(--padding-end);
  padding-inline-end: var(--padding-end);
  padding-top: var(--padding-top);
  padding-bottom: var(--padding-bottom);
}

.img-box {
  width: 100%;
  height: 80px;
  .img-wrapper {
    .add-img {
      height: 80px;
      vertical-align: middle;
      line-height: 66px;
      display: inline-block;
    }
    .remove-img {

      display: inline-block;
      line-height: 70px;
      vertical-align: middle;
      ion-icon {
        width: 30px;
        height: 30px;
        vertical-align: middle;
        border: none;
        line-height: 80px;
      }
    }
    ion-icon, img {

      width: 70px;
      height: 70px;
      color: #e8e8e8;
      border: 1px solid #e8e8e8;
      line-height: 30px;
      vertical-align: top;
      display: inline-block;
      margin: 5px;
    }

  }
}

ion-label {
  &.label-fixed {
    -ms-flex: 0 0 120px;
    flex: 0 0 120px;
  }
  ion-text[color="danger"] {
    font-size: 1em;
  }
}
.placeholder {
  color: #B1B1B1;
}
