import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.scss'],
})
export class CardComponent implements OnInit {
  @Input() disabled = false

  constructor() { }

  ngOnInit() {}

  get headerClass() {
    return 'asset-card-header' + (this.disabled ? ' disabled' : '')
  }
  get bodyClass() {
    return 'asset-card-body' + (this.disabled ? ' disabled' : '')
  }
}
