import { Injectable, OnInit } from '@angular/core';
import {LoadingController} from '@ionic/angular'

@Injectable({
  providedIn: 'root'
})
export class LoadingService {

  public el: any
  public id =  'loading-service'
  constructor(private loadingController: LoadingController) {}

  async start() {
    if (this.isExist()) { return }
    this.el = await this.loadingController.create({
      message: 'Loading...',
      // duration: 2000
      id: this.id
    });
    await this.el.present()
  }
  async end() {
    if (this.isExist()) {
      await this.el.dismiss()
      window.document.querySelectorAll('#loading-service').forEach(l => l.remove())
    }
  }
  isExist() {
    const dom = window.document.getElementById(this.id)
    return !!dom && this.el && this.el.dismiss
  }
}
