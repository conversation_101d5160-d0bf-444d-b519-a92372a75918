import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
// import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

// import { SelectLocationPage } from './select-location.page';

// const routes: Routes = [
//   {
//     path: '',
//     component: SelectLocationPage
//   }
// ];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    // RouterModule.forChild(routes)
  ],
  // declarations: [SelectLocationPage],
  // entryComponents: [SelectLocationPage],
  // exports: [SelectLocationPage],
})
export class SelectLocationPageModule {}
