
import {Platform} from '@ionic/angular'
import { Injectable } from '@angular/core';
import {SqlService} from '@services/sql/sql.service'
import {dateFormat} from '../../../utils'
import {AssetService} from '@services/api/asset.service'

@Injectable({
  providedIn: 'root'
})
export class RelocationService {

  constructor(
    private platform: Platform,
    private sql: SqlService,
    private asset: AssetService,
    ) {
      this.platform.ready()
        .then(() => {
          this.sql.init()
        })
  }
  get isInit() {
    return this.sql.isInit
  }

  /* 更新轉移地址列表 */
  remoteLoadingLocation() {
    return new Promise((resolve, reject) => {
      this.asset.getAppRelocationLocation().then((res: any) => {
        console.log(res)
        const sqlText = 'INSERT INTO tbl_all_location (no, location_name, location_ename, pid, isParent, child) VALUES (?,?,?,?,?,?)'

        this.sql.db.transaction(tx => {
          // 清空舊數據
          tx.executeSql('delete from tbl_all_location');
          tx.executeSql('update sqlite_sequence SET seq = 0 where name ="tbl_all_location"');
          // tslint:disable-next-line:prefer-for-of
          for (let i = 0; i < res.length; i++) {
            const item = res[i]
            tx.executeSql(sqlText, [item.no, item.location_name, item.location_ename, item.pid, item.isParent, JSON.stringify(item.child)]);
          }
        }).catch(err => {
          console.log('更新轉移地址列表 - 失敗1', err)
          reject(err)
        }).then(() => {
          console.log('更新轉移地址列表 - 完成')
          resolve(res)
        })
      }).catch(err => {
        console.log('更新轉移地址列表 - 失敗2', err)
        reject(err)
      })
    })
  }
  /* 獲取轉移地址 */
  getLocation() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_all_location order by id desc', [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              try {
                if (item.child) {
                  item.child = JSON.parse(item.child)
                }
              } catch (e) {
                console.error(e)
              }
              arr.push({
                ...item
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }

  getLocationArray(list, codes) {
    const arr = []
    for (const item of list) {
      if (codes.includes(item.no)) {
        const newItem = Object.assign({}, item)
        newItem.child = null
        newItem.isParent = false
        arr.push(newItem)
      }
      if (Array.isArray(item.child) && item.child.length > 0) {
        const child = this.getLocationArray(item.child, codes)
        if (child.length > 0) {
          arr.push(...child)
        }
      }
    }
    return arr
  }
  getLocationItem(list, no) {
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.no === no) {
        return item
      }
      if (item.child && item.child.length > 0) {
        const child = this.getLocationItem(item.child, no)
        if (child) {
          return child
        }
      }
    }
    return null
  }

  /* 獲取本地轉移列表 */
  all() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_relocation order by id desc', [],
          (x, rs) => {
            const arr = []
            for (let i = 0; i < rs.rows.length; i++) {
              const item = rs.rows.item(i)
              arr.push({
                ...item
              })
            }
            resolve(arr)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  /* 增加獲取本地轉移 */
  // tslint:disable-next-line:max-line-length
  add({ property_barcode, property_name, property_no, department_name, department_ename, kind_name, kind_ename,
        serial_no, property_maint_fmdate, property_maint_todate, supplier_name, property_status,
        location_name, location_ename, property_location, property_location_serial_no, property_location_serial_ctime }: any) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          // tslint:disable-next-line:max-line-length
          'INSERT INTO tbl_relocation (property_barcode, property_name, property_no, department_name, department_ename, kind_name, kind_ename,' +
          'serial_no, property_maint_fmdate, property_maint_todate, supplier_name, property_status,' +
          'location_name, location_ename, property_location, property_location_serial_no, property_location_serial_ctime' +
          ') VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)',
          // tslint:disable-next-line:max-line-length
          [property_barcode, property_name, property_no, department_name, department_ename, kind_name, kind_ename,
            serial_no, property_maint_fmdate, property_maint_todate, supplier_name, property_status,
            location_name, location_ename, property_location, property_location_serial_no, property_location_serial_ctime],
          (x, rs) => {
            console.log(x, rs)
            this.all().then(arr => {
              resolve(rs.insertId)
            }).catch(err => {
              console.error(err)
              reject(err)
            })
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }

  get(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql('SELECT * FROM tbl_relocation WHERE id=(?)', [id],
          (x, rs) => {
            if (rs && rs.rows && rs.rows.length > 0 ) {
              const item = rs.rows.item(0)
              console.log('sql row:', item)

              const data = {
                ...item
              }
              resolve(data)
            } else {
              reject(rs.rows)
            }
          }, (x, error) => {
            console.error('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  delete(id) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_relocation WHERE id=(?)',
          [id],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  // tslint:disable-next-line:max-line-length
  update({ id, property_barcode, property_name, property_no, department_name, department_ename, kind_name, kind_ename,
           serial_no, property_maint_fmdate, property_maint_todate, supplier_name, property_status,
           location_name, location_ename, property_location_serial_no, property_location_serial_ctime,
           property_location, change_num, relocation_location_no, property_relocation_remark}: any) {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        const values = []
        let sqlStr = 'UPDATE tbl_relocation SET '
        if (property_barcode !== undefined) { sqlStr += 'property_barcode=(?),'; values.push(property_barcode); }
        if (property_name !== undefined) { sqlStr += 'property_name=(?),'; values.push(property_name); }
        if (property_no !== undefined) { sqlStr += 'property_no=(?),'; values.push(property_no); }
        if (department_name !== undefined) { sqlStr += 'department_name=(?),'; values.push(department_name); }
        if (department_ename !== undefined) { sqlStr += 'department_ename=(?),'; values.push(department_ename); }
        if (kind_name !== undefined) { sqlStr += 'kind_name=(?),'; values.push(kind_name); }
        if (kind_ename !== undefined) { sqlStr += 'kind_ename=(?),'; values.push(kind_ename); }
        if (serial_no !== undefined) { sqlStr += 'serial_no=(?),'; values.push(serial_no); }
        if (property_maint_fmdate !== undefined) { sqlStr += 'property_maint_fmdate=(?),'; values.push(property_maint_fmdate); }
        if (property_maint_todate !== undefined) { sqlStr += 'property_maint_todate=(?),'; values.push(property_maint_todate); }
        if (supplier_name !== undefined) { sqlStr += 'supplier_name=(?),'; values.push(supplier_name); }
        if (property_status !== undefined) { sqlStr += 'property_status=(?),'; values.push(property_status); }
        if (location_name !== undefined) { sqlStr += 'location_name=(?),'; values.push(location_name); }
        if (location_ename !== undefined) { sqlStr += 'location_ename=(?),'; values.push(location_ename); }
        if (property_location_serial_no !== undefined) { sqlStr += 'property_location_serial_no=(?),'; values.push(property_location_serial_no); }
        if (property_location_serial_ctime !== undefined) { sqlStr += 'property_location_serial_ctime=(?),'; values.push(property_location_serial_ctime); }
        if (property_location !== undefined) { sqlStr += 'property_location=(?),'; values.push(property_location); }
        if (change_num !== undefined) { sqlStr += 'change_num=(?),'; values.push(change_num); }
        if (relocation_location_no !== undefined) { sqlStr += 'relocation_location_no=(?),'; values.push(relocation_location_no); }
        if (property_relocation_remark !== undefined) { sqlStr += 'property_relocation_remark=(?),'; values.push(property_relocation_remark); }

        if (sqlStr.length < 27) {
          reject()
          return
        }
        sqlStr = sqlStr.substr(0, sqlStr.length - 1);
        sqlStr += ' WHERE id=(?)'
        values.push(id)
        tx.executeSql(
          // tslint:disable-next-line:max-line-length
          // 'UPDATE tbl_relocation SET property_barcode=(?), property_name=(?), property_no=(?), department_name=(?), department_ename=(?),' +
          // ' kind_name=(?), kind_ename=(?), serial_no=(?), property_maint_fmdate=(?), property_maint_todate=(?), supplier_name=(?),' +
          // tslint:disable-next-line:max-line-length
          // ' property_status=(?), location_name=(?), location_ename=(?), property_location_serial_no=(?), property_location_serial_ctime=(?)' +
          // ' property_relocation_remark=(?) WHERE id=(?)',
          sqlStr,
          values,
          // tslint:disable-next-line:max-line-length
          // [property_barcode, property_name, property_no, department_name, department_ename,
          //   kind_name, kind_ename, serial_no, property_maint_fmdate, property_maint_todate, supplier_name,
          //   property_status, location_name, location_ename, property_location_serial_no, property_location_serial_ctime, id],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
  clear() {
    return new Promise((resolve, reject) => {
      if (!this.sql.isInit) {
        reject()
        return
      }
      // const now = dateFormat(new Date(), 'yyyy-MM-dd HH:mm')
      this.sql.db.transaction(tx => {
        tx.executeSql(
          'DELETE FROM tbl_relocation',
          [],
          (x, rs) => {
            resolve(true)
          }, (x, error) => {
            console.log('SELECT error: ' + error.message);
            reject(error)
          });
      })
    })
  }
}
