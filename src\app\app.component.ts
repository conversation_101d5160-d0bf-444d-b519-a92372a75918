import { Component } from '@angular/core';

import { Platform, ToastController } from '@ionic/angular';
import { SplashScreen } from '@ionic-native/splash-screen/ngx';
import { StatusBar } from '@ionic-native/status-bar/ngx';

import { StoreService } from './services/store/store.service';
import {TranslateService} from '@ngx-translate/core';
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import {SqlService} from '@services/sql/sql.service'
import {AndroidNotch} from '@awesome-cordova-plugins/android-notch/ngx';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent {
  private backButtonPressed = false
  private subscription: any
  constructor(private platform: Platform,
              private splashScreen: SplashScreen,
              private statusBar: StatusBar,
              private storeService: StoreService,
              private toastCtrl: ToastController,
              public translate: TranslateService,
              private nativeStorage: StorageNativeService,
              private sql: SqlService,
              private androidNotch: AndroidNotch,
  ) {
    this.initTranslate()
    this.initializeApp();
  }

    // tslint:disable-next-line:use-lifecycle-interface
  ngAfterContentInit() {
    this.platform.ready()
      .then(async () => {
        if (this.platform.is('android')) {
            this.detectInsets()
        }
      })
  }
    ionViewWillEnter() {
        this.detectInsets()
    }

  detectInsets() {
    console.log('detectInsets', 1)
    if (this.androidNotch) {
        // this.statusBar.overlaysWebView(true)
        // StatusBar.overlaysWebView({overlay: true})
        console.log('detectInsets', 2)
        const style = document.documentElement.style;
        console.log('detectInsets', 3, style)
        console.log('detectInsets', 4, this.androidNotch)
        this.androidNotch.getInsetTop().then((px: any) => {
            style.setProperty('--notch-inset-top', Math.max(px, 25) + 'px');
            console.log('detectInsets', 4, px)
        }, (err) => console.error('Failed to get insets top:', err));

        // @ts-ignore
        this.androidNotch.getInsetRight().then((px: any) => {
            style.setProperty('--notch-inset-right', px + 'px');
        }, (err) => console.error('Failed to get insets right:', err));

        // @ts-ignore
        this.androidNotch.getInsetBottom().then((px: any) => {
            style.setProperty('--notch-inset-bottom', px + 'px');
        }, (err) => console.error('Failed to get insets bottom:', err));

        // @ts-ignore
        this.androidNotch.getInsetLeft().then((px: any) => {
            style.setProperty('--notch-inset-left', px + 'px');
        }, (err) => console.error('Failed to get insets left:', err));

        const softButton = (screen.height - this.platform.height());
        console.log('detectInsets', 5, softButton)
        const body: any = document.querySelectorAll('body');
        body.forEach((e: any) => {
            e.classList.add('soft-button');
            e.style = '--ion-soft-button:' + softButton + 'px';
        });
        // try {
        //     const { top, bottom } = await SafeArea.getInsets();
        //     document.documentElement.style.setProperty('--ion-safe-area-top', top + 'px');
        //     document.documentElement.style.setProperty('--ion-safe-area-bottom', bottom + 'px');
        // } catch (e) {
        //     console.error(e);
        // }
    }
}

  initializeApp() {
    this.platform.ready()
        .then(async () => {
          // this.statusBar.styleDefault();
          this.splashScreen.hide();
          await new Promise(resolve => setTimeout(resolve, 500))
          this.statusBar.backgroundColorByHexString('#D5E6F6')
          await this.storeService.initPermissions()
          this.sql.init()

          // 兩次後退 退出APP
          // this.subscription = this.platform.backButton.subscribe(() => {
          //   if (this.backButtonPressed) {
          //     // @ts-ignore
          //     navigator.app.exitApp();
          //   } else {
          //     this.handleExitSuccess();
          //     // 标记为true
          //     this.backButtonPressed = true;
          //     // 两秒后标记为false，如果退出的话，就不会执行了
          //     setTimeout(() => this.backButtonPressed = false, 2000);
          //   }
          // });
        });
  }

  async initTranslate() {
    this.translate.addLangs(['en', 'cn']);
    this.translate.setDefaultLang('cn');
    let lang = 'cn'
    try {
      const nl = await this.nativeStorage.getItem('lang')
      if (nl === 'cn' || nl === 'en') {
        lang = nl
      } else {
        const browserLang = this.translate.getBrowserLang();
        this.translate.use(browserLang.match(/en|cn/) ? browserLang : 'cn');
        return
      }
    } catch (e) {

    }
    this.translate.use(lang);
  }

  // async handleExitSuccess() {
  //   const toast = await this.toastCtrl.create({
  //     message: '再按一次退出应用',
  //     duration: 2000,
  //     // position: 'middle'
  //     position: 'bottom'
  //   });
  //   toast.present();
  // }
}
