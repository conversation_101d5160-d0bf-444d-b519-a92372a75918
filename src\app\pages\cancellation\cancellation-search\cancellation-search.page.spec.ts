import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CancellationSearchPage } from './cancellation-search.page';

describe('CancellationSearchPage', () => {
  let component: CancellationSearchPage;
  let fixture: ComponentFixture<CancellationSearchPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CancellationSearchPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CancellationSearchPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
