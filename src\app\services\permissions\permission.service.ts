import { Injectable } from '@angular/core';
import {StorageNativeService} from '@services/storageNative/storage-native.service'
import {Platform} from '@ionic/angular'

@Injectable({
  providedIn: 'root'
})
export class PermissionService {

  public permissions: any = {
    AssetCheck: '1111',
    Cancellation: '0000',
    Relocation: '0000',
    PropertyAllocation: '0000',
    Borrow: '0000',
    PropertyReturn: '0000',
  }
  public isInit = false
  constructor(
    private nativeStorage: StorageNativeService,
    private platform: Platform,
    ) {
    this.platform.ready().then(() => {
      this.init()
        .then(() => {
          this.isInit = true
        })
        .catch((e) => { console.error(e) })
    })
  }

  async init() {
    let permissions
    try {
      permissions = await this.nativeStorage.getItem('permissions')
    } catch (e) {
      console.error(e)
    }
    if (permissions) {
      const keys = Object.keys(permissions)
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        if (permissions[key]) {
          this.permissions[key] = permissions[key]
        }
      }
    }
  }

  hasModule(name) {
    if (!this.isInit) { return false }
    return this.permissions.hasOwnProperty(name) && this.permissions[name]
  }

  hasPermission(code, permission) {
    if (!this.isInit) { return false }
    const item = this.permissions[code]
    if (item && item[permission]) {
      return item[permission] === '1'
    }
    return false
  }
  hasPermission_View(code) {
    return this.hasPermission(code, 0)
  }
  hasPermission_Add(code) {
    return this.hasPermission(code, 1)
  }
  hasPermission_Edit(code) {
    return this.hasPermission(code, 2)
  }
  hasPermission_Delete(code) {
    return this.hasPermission(code, 3)
  }
}
