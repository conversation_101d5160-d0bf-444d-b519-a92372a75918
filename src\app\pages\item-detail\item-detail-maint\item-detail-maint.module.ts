import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { ItemDetailMaintPage } from './item-detail-maint.page';

const routes: Routes = [
  {
    path: '',
    component: ItemDetailMaintPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes)
  ],
  declarations: [ItemDetailMaintPage]
})
export class ItemDetailMaintPageModule {}
