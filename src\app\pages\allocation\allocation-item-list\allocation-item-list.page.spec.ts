import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AllocationItemListPage } from './allocation-item-list.page';

describe('AllocationItemListPage', () => {
  let component: AllocationItemListPage;
  let fixture: ComponentFixture<AllocationItemListPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AllocationItemListPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AllocationItemListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
