import {Component, Input, OnInit} from '@angular/core'
import {UtilsService} from '../../utils/utils.service'
import {Router} from '@angular/router'

@Component({
  selector: 'app-information',
  templateUrl: './information.component.html',
  styleUrls: ['./information.component.scss'],
})
export class InformationComponent implements OnInit {

  @Input() data: any
  private isErr = false
  constructor(
    public utils: UtilsService,
    private router: Router
  ) {
  }

  ngOnInit() {
  }

  get imgUrl() {
    return this.utils.getImgUrl(this.data.photo, this.isErr)
    // if (this.data.photo && !this.isErr) {
    //   return this.data.photo
    // } else {
    //   return 'assets/images/nopic.jpg'
    // }
  }
  imgError() {
    this.isErr = true
  }
  toDetail() {

    this.router.navigate(
      // ['/lb-reader/book-info-reader'],
      ['information'],
      {
        queryParams: {
          'information-id': this.data.information_id,
        },

      }
    )
  }
  get content() {
    // let content = this.data.content.replace(/<[^>]*>/g, '')
    let div = document.createElement('div')
    div.innerHTML = this.data.content
    let content = div.innerText
    div = undefined
    content = content.substring(0, 50)
    return content + '...'
    // return this.data.content.replace(/<[^>]*>/g, '')
    // return $sce.trustAsHtml(this.data.content)
  }
}
