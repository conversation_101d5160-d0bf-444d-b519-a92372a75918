import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-empty',
  templateUrl: './empty.component.html',
  styleUrls: ['./empty.component.scss'],
})
export class EmptyComponent implements OnInit {
  @Input() message = "No data"
  @Input() mode = "white"

  constructor() { }

  ngOnInit() {}

  get headerClass() {
    return 'asset-empty-header'
  }
  get bodyClass() {
    let name = 'asset-empty-body'
    if (this.mode === 'dark') {
      name += ' asset-empty-body-dark'
    }
    return  name
  }
}
